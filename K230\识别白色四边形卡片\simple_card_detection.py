# 简化版白色四边形卡片识别程序
# 基于博主04矩形识别代码改造，专注于卡片检测

import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms

sensor = None

try:
    print("白色四边形卡片识别程序启动")

    # 基于博主教学代码的摄像头配置
    sensor = Sensor(width=640, height=640)
    sensor.reset()

    # 设置摄像头参数
    sensor.set_framesize(width=640, height=640)
    sensor.set_pixformat(Sensor.RGB565)

    Display.init(Display.LT9611, to_ide=True)
    # 初始化媒体管理器
    MediaManager.init()
    # 启动 sensor
    sensor.run()
    clock = time.clock()

    while True:
        clock.tick()
        os.exitpoint()
        img = sensor.snapshot(chn=CAM_CHN_ID_0)

        # 基于04矩形识别代码的矩形检测方法
        # 转换为灰度图进行处理
        img_rect = img.to_grayscale(copy=True)
        # 二值化处理，突出白色区域
        img_rect = img_rect.binary([(82, 212)])
        # 矩形检测
        rects = img_rect.find_rects(threshold=10000)

        if rects is not None and len(rects) > 0:
            print(f"检测到 {len(rects)} 个矩形")
            
            # 处理检测到的每个矩形
            for i, rect in enumerate(rects):
                # 获取四个角点坐标
                corner = rect.corners()
                
                # 基于04矩形识别代码的绘制方法
                # 绘制四边形的四条边
                img.draw_line(corner[0][0], corner[0][1], corner[1][0], corner[1][1], 
                            color=(0, 255, 0), thickness=5)
                img.draw_line(corner[2][0], corner[2][1], corner[1][0], corner[1][1], 
                            color=(0, 255, 0), thickness=5)
                img.draw_line(corner[2][0], corner[2][1], corner[3][0], corner[3][1], 
                            color=(0, 255, 0), thickness=5)
                img.draw_line(corner[0][0], corner[0][1], corner[3][0], corner[3][1], 
                            color=(0, 255, 0), thickness=5)
                
                # 绘制角点
                colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)]
                for j, point in enumerate(corner):
                    img.draw_circle(point[0], point[1], 8, color=colors[j], thickness=3)
                
                # 输出顶点坐标
                print(f"矩形{i+1}的四个顶点坐标:")
                corner_names = ["角点1", "角点2", "角点3", "角点4"]
                for j, point in enumerate(corner):
                    print(f"  {corner_names[j]}: ({point[0]}, {point[1]})")
                
                # 计算中心点 - 基于PID激光点回中代码的方法
                center_x = sum([corner[k][0] for k in range(4)]) / 4
                center_y = sum([corner[k][1] for k in range(4)]) / 4
                img.draw_circle(int(center_x), int(center_y), 6, 
                              color=(255, 0, 255), thickness=3, fill=True)
                print(f"  中心点: ({int(center_x)}, {int(center_y)})")
                print("---")

        # 基于博主教学代码的FPS显示
        img.draw_string_advanced(50, 50, 80, "fps: {}".format(clock.fps()), color=(255, 0, 0))
        img.compressed_for_ide()
        Display.show_image(img)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    # 基于博主教学代码的清理流程
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()