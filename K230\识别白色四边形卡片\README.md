# 白色四边形卡片识别程序

基于庐山派K230官方例程和博主教学代码开发的白色四边形卡片识别程序。

## 程序说明

### 1. simple_card_detection.py
- **基础版本**，直接基于博主04矩形识别代码改造
- 使用灰度二值化 + find_rects() 方法
- 适合环境光线稳定的场景
- 分辨率: 640x640

### 2. enhanced_card_detection.py  
- **增强版本**，结合色块检测和矩形检测
- 先用find_blobs()检测白色区域，再在ROI内检测矩形
- 适合复杂环境，检测精度更高
- 分辨率: 1024x768

### 3. white_rectangle_detection.py
- **完整版本**，融合A4纸检测代码的所有功能
- 包含中心点判断、最佳矩形选择等高级功能
- 适合需要精确检测的应用场景
- 分辨率: 640x480

## 使用方法

1. 选择适合的程序版本
2. 将白色四边形卡片放在摄像头前
3. 运行程序，观察检测结果
4. 根据控制台输出获取四个顶点的像素坐标

## 参数调节

### 二值化阈值
```python
img_rect.binary([(82, 212)])  # 调节这两个数值
```

### 白色色块检测阈值 (LAB颜色空间)
```python
[(41, 100, -20, 20, -20, 20)]  # L, A, B的最小和最大值
```

### 矩形检测阈值
```python
find_rects(threshold=10000)  # 调节数值过滤噪声
```

### 面积阈值
```python
pixels_threshold=5000  # 最小像素数
```

## 输出格式

程序会在控制台输出四个顶点的像素坐标：
```
矩形1的四个顶点坐标:
  角点1: (x1, y1)
  角点2: (x2, y2) 
  角点3: (x3, y3)
  角点4: (x4, y4)
  中心点: (cx, cy)
```

## 调试建议

1. 光线环境要稳定，避免阴影
2. 白色卡片与背景要有足够对比度
3. 卡片大小建议占画面的1/4到1/2
4. 根据实际环境调节颜色阈值参数

## 代码来源

- 基于庐山派K230官方例程: examples/11-Feature-Detection/find_rects.py
- 参考A4纸检测代码: 识别A4纸/a4_tracking_style.py  
- 融合博主教学代码: code - 副本/04矩形识别与常见的图像处理.py
- 优化技巧来源: code - 副本/05色块追踪与线段识别.py