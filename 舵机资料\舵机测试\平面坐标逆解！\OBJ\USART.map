Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    main.o(i.main) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to usart.o(i.uart_init) for uart_init
    main.o(i.main) refers to usart.o(i.uart2_init) for uart2_init
    main.o(i.main) refers to led.o(i.LED_Init) for LED_Init
    main.o(i.main) refers to beep.o(i.BEEP_Init) for BEEP_Init
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to beep.o(i.BEEP_Sound) for BEEP_Sound
    main.o(i.main) refers to usart.o(i.Servo_LoadMotor) for Servo_LoadMotor
    main.o(i.main) refers to usart.o(i.Servo_MoveToPosition) for Servo_MoveToPosition
    main.o(i.main) refers to usart.o(i.Screen_UpdateCoordinateDisplay) for Screen_UpdateCoordinateDisplay
    main.o(i.main) refers to usart.o(i.Screen_ProcessCommand) for Screen_ProcessCommand
    main.o(i.main) refers to usart.o(i.Screen_UpdateAngleDisplay) for Screen_UpdateAngleDisplay
    main.o(i.main) refers to main.o(.data) for display_counter
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(i.SystemInit) refers to system_stm32f4xx.o(i.SetSysClock) for SetSysClock
    led.o(i.LED_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    led.o(i.LED_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    led.o(i.LED_Init) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    beep.o(i.BEEP_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    beep.o(i.BEEP_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    beep.o(i.BEEP_Init) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    beep.o(i.BEEP_Sound) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    beep.o(i.BEEP_Sound) refers to delay.o(i.delay_ms) for delay_ms
    beep.o(i.BEEP_Sound) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    key.o(i.KEY_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    key.o(i.KEY_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    key.o(i.KEY_Scan) refers to stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.KEY_Scan) refers to delay.o(i.delay_ms) for delay_ms
    key.o(i.KEY_Scan) refers to key.o(.data) for key_up
    delay.o(i.delay_init) refers to misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    delay.o(i.delay_init) refers to delay.o(.data) for fac_us
    delay.o(i.delay_ms) refers to delay.o(i.delay_xms) for delay_xms
    delay.o(i.delay_us) refers to delay.o(.data) for fac_us
    delay.o(i.delay_xms) refers to delay.o(.data) for fac_ms
    usart.o(i.Plane_CoordinateInverse) refers to f2d.o(.text) for __aeabi_f2d
    usart.o(i.Plane_CoordinateInverse) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    usart.o(i.Plane_CoordinateInverse) refers to d2f.o(.text) for __aeabi_d2f
    usart.o(i.Plane_CoordinateInverse) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    usart.o(i.Plane_CoordinateInverse) refers to dmul.o(.text) for __aeabi_dmul
    usart.o(i.Screen_ProcessCommand) refers to beep.o(i.BEEP_Sound) for BEEP_Sound
    usart.o(i.Screen_ProcessCommand) refers to usart.o(i.USART2_SendArray) for USART2_SendArray
    usart.o(i.Screen_ProcessCommand) refers to delay.o(i.delay_ms) for delay_ms
    usart.o(i.Screen_ProcessCommand) refers to usart.o(i.Screen_RequestCoordinateInput) for Screen_RequestCoordinateInput
    usart.o(i.Screen_ProcessCommand) refers to memcpya.o(.text) for __aeabi_memcpy4
    usart.o(i.Screen_ProcessCommand) refers to usart.o(i.Servo_MoveToCoordinate) for Servo_MoveToCoordinate
    usart.o(i.Screen_ProcessCommand) refers to usart.o(.data) for SCREEN_RX_STA
    usart.o(i.Screen_ProcessCommand) refers to usart.o(.bss) for SCREEN_RX_BUF
    usart.o(i.Screen_ProcessCommand) refers to usart.o(.constdata) for .constdata
    usart.o(i.Screen_ProcessCommand) refers to usart.o(i.Servo_CalculateChecksum) for Servo_CalculateChecksum
    usart.o(i.Screen_ProcessCommand) refers to usart.o(i.USART_SendArray) for USART_SendArray
    usart.o(i.Screen_ProcessCommand) refers to usart.o(i.Servo_AngleFeedbackTest) for Servo_AngleFeedbackTest
    usart.o(i.Screen_ProcessCommand) refers to usart.o(i.Screen_UpdateCoordinateDisplay) for Screen_UpdateCoordinateDisplay
    usart.o(i.Screen_RequestCoordinateInput) refers to usart.o(i.USART2_SendArray) for USART2_SendArray
    usart.o(i.Screen_RequestCoordinateInput) refers to delay.o(i.delay_ms) for delay_ms
    usart.o(i.Screen_UpdateAngleDisplay) refers to usart.o(i.USART2_SendArray) for USART2_SendArray
    usart.o(i.Screen_UpdateAngleDisplay) refers to delay.o(i.delay_ms) for delay_ms
    usart.o(i.Screen_UpdateAngleDisplay) refers to usart.o(.data) for SERVO1_POSITION
    usart.o(i.Screen_UpdateCoordinateDisplay) refers to usart.o(i.USART2_SendArray) for USART2_SendArray
    usart.o(i.Screen_UpdateCoordinateDisplay) refers to delay.o(i.delay_ms) for delay_ms
    usart.o(i.Servo_AngleFeedbackTest) refers to usart.o(i.USART2_SendArray) for USART2_SendArray
    usart.o(i.Servo_AngleFeedbackTest) refers to delay.o(i.delay_ms) for delay_ms
    usart.o(i.Servo_AngleFeedbackTest) refers to usart.o(i.Servo_TestPositionAccuracy) for Servo_TestPositionAccuracy
    usart.o(i.Servo_AngleFeedbackTest) refers to usart.o(.constdata) for .constdata
    usart.o(i.Servo_LoadMotor) refers to usart.o(i.Servo_CalculateChecksum) for Servo_CalculateChecksum
    usart.o(i.Servo_LoadMotor) refers to usart.o(i.USART_SendArray) for USART_SendArray
    usart.o(i.Servo_MoveToCoordinate) refers to usart.o(i.Plane_CoordinateInverse) for Plane_CoordinateInverse
    usart.o(i.Servo_MoveToCoordinate) refers to usart.o(i.AngleToServoPosition) for AngleToServoPosition
    usart.o(i.Servo_MoveToCoordinate) refers to usart.o(i.Servo_MoveToPosition) for Servo_MoveToPosition
    usart.o(i.Servo_MoveToCoordinate) refers to delay.o(i.delay_ms) for delay_ms
    usart.o(i.Servo_MoveToCoordinate) refers to usart.o(i.Screen_UpdateCoordinateDisplay) for Screen_UpdateCoordinateDisplay
    usart.o(i.Servo_MoveToCoordinate) refers to beep.o(i.BEEP_Sound) for BEEP_Sound
    usart.o(i.Servo_MoveToCoordinate) refers to usart.o(i.USART2_SendArray) for USART2_SendArray
    usart.o(i.Servo_MoveToCoordinate) refers to usart.o(.data) for SERVO1_POSITION
    usart.o(i.Servo_MoveToPosition) refers to usart.o(i.Servo_CalculateChecksum) for Servo_CalculateChecksum
    usart.o(i.Servo_MoveToPosition) refers to usart.o(i.USART_SendArray) for USART_SendArray
    usart.o(i.Servo_ProcessPacket) refers to usart.o(i.Servo_CalculateChecksum) for Servo_CalculateChecksum
    usart.o(i.Servo_ProcessPacket) refers to usart.o(.data) for SERVO_RX_CNT
    usart.o(i.Servo_ProcessPacket) refers to usart.o(.bss) for SERVO_RX_BUF
    usart.o(i.Servo_ReadPosition) refers to usart.o(i.Servo_CalculateChecksum) for Servo_CalculateChecksum
    usart.o(i.Servo_ReadPosition) refers to usart.o(i.USART_SendArray) for USART_SendArray
    usart.o(i.Servo_ReadPosition) refers to delay.o(i.delay_ms) for delay_ms
    usart.o(i.Servo_ReadPosition) refers to usart.o(.data) for servo_feedback_received
    usart.o(i.Servo_SetMotorMode) refers to usart.o(i.Servo_CalculateChecksum) for Servo_CalculateChecksum
    usart.o(i.Servo_SetMotorMode) refers to usart.o(i.USART_SendArray) for USART_SendArray
    usart.o(i.Servo_TestPositionAccuracy) refers to usart.o(i.Servo_MoveToPosition) for Servo_MoveToPosition
    usart.o(i.Servo_TestPositionAccuracy) refers to delay.o(i.delay_ms) for delay_ms
    usart.o(i.Servo_TestPositionAccuracy) refers to usart.o(i.Servo_ReadPosition) for Servo_ReadPosition
    usart.o(i.Servo_TestPositionAccuracy) refers to usart.o(i.USART2_SendArray) for USART2_SendArray
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART1_IRQHandler) refers to usart.o(i.Servo_ProcessPacket) for Servo_ProcessPacket
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for SERVO_RX_STA
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for SERVO_RX_BUF
    usart.o(i.USART2_IRQHandler) refers to stm32f4xx_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART2_IRQHandler) refers to stm32f4xx_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART2_IRQHandler) refers to usart.o(.data) for SCREEN_RX_CNT
    usart.o(i.USART2_IRQHandler) refers to usart.o(.bss) for SCREEN_RX_BUF
    usart.o(i.USART2_SendArray) refers to stm32f4xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.USART2_SendArray) refers to stm32f4xx_usart.o(i.USART_SendData) for USART_SendData
    usart.o(i.USART_SendArray) refers to stm32f4xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.USART_SendArray) refers to stm32f4xx_usart.o(i.USART_SendData) for USART_SendData
    usart.o(i.uart2_init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    usart.o(i.uart2_init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart.o(i.uart2_init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart.o(i.uart2_init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart2_init) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart2_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(i.uart2_init) refers to stm32f4xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart2_init) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.uart_init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart.o(i.uart_init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(.constdata) refers to usart.o(.conststring) for .conststring
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_rcc.o(i.RCC_GetClocksFreq) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f4xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f4xx_syscfg.o(i.SYSCFG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_usart.o(i.USART_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    atan2.o(i.__hardfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dadd.o(.text) for __aeabi_dsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    cos.o(i.__hardfp_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.__hardfp_cos) refers to errno.o(i.__set_errno) for __set_errno
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos.o(i.__hardfp_cos) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos.o(i.__hardfp_cos) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos.o(i.__hardfp_cos) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos.o(i.__softfp_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.__softfp_cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos.o(i.cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos_x.o(i.____hardfp_cos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_x.o(i.____hardfp_cos$lsc) refers to errno.o(i.__set_errno) for __set_errno
    cos_x.o(i.____hardfp_cos$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos_x.o(i.____hardfp_cos$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos_x.o(i.____hardfp_cos$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos_x.o(i.____hardfp_cos$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos_x.o(i.____softfp_cos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_x.o(i.____softfp_cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    cos_x.o(i.__cos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_x.o(i.__cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    atan.o(i.__hardfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to dadd.o(.text) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers to dfixi.o(.text) for __aeabi_d2iz
    cos_i.o(i.__kernel_cos) refers to dmul.o(.text) for __aeabi_dmul
    cos_i.o(i.__kernel_cos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    cos_i.o(i.__kernel_cos) refers to dadd.o(.text) for __aeabi_dsub
    cos_i.o(i.__kernel_cos) refers to cos_i.o(.constdata) for .constdata
    cos_i.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to dadd.o(.text) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to fabs.o(i.fabs) for fabs
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(.text) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfixi.o(.text) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflti.o(.text) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to dfltui.o(.text) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sin_i.o(i.__kernel_sin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sin_i.o(i.__kernel_sin) refers to dmul.o(.text) for __aeabi_dmul
    sin_i.o(i.__kernel_sin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i.o(i.__kernel_sin) refers to dadd.o(.text) for __aeabi_dsub
    sin_i.o(i.__kernel_sin) refers to sin_i.o(.constdata) for .constdata
    sin_i.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers to dmul.o(.text) for __aeabi_dmul
    sin_i_x.o(i.____kernel_sin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i_x.o(i.____kernel_sin$lsc) refers to dadd.o(.text) for __aeabi_dsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to sin_i_x.o(.constdata) for .constdata
    sin_i_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (192 bytes).
    Removing system_stm32f4xx.o(.data), (20 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing beep.o(.rev16_text), (4 bytes).
    Removing beep.o(.revsh_text), (4 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(i.KEY_Init), (68 bytes).
    Removing key.o(i.KEY_Scan), (128 bytes).
    Removing key.o(.data), (1 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(i.delay_us), (76 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(i.Servo_SetMotorMode), (80 bytes).
    Removing usart.o(i._sys_exit), (4 bytes).
    Removing usart.o(i.fputc), (28 bytes).
    Removing startup_stm32f40_41xxx.o(HEAP), (512 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_DeInit), (312 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_PinLockConfig), (34 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_StructInit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ToggleBits), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_DeInit), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetFlagStatus), (64 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSEConfig), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_I2SCLKConfig), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEConfig), (44 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEModeConfig), (36 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO1Config), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO2Config), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLConfig), (36 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SConfig), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAIConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKConfig), (60 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_CompensationCellCmd), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_DeInit), (22 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig), (64 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_GetCompensationCellStatus), (24 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemoryRemapConfig), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemorySwappingBank), (12 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearITPendingBit), (30 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockInit), (32 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f4xx_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_DeInit), (240 bytes).
    Removing stm32f4xx_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f4xx_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing dneg.o(.text), (6 bytes).

130 unused section(s) (total 3933 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  useno.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos_x.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos.o ABSOLUTE
    ../mathlib/cos_i.c                       0x00000000   Number         0  cos_i.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i_x.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i.o ABSOLUTE
    ..\CORE\startup_stm32f40_41xxx.s         0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    ..\FWLIB\src\misc.c                      0x00000000   Number         0  misc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_gpio.c            0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rcc.c             0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_syscfg.c          0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_usart.c           0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\HARDWARE\BEEP\beep.c                  0x00000000   Number         0  beep.o ABSOLUTE
    ..\HARDWARE\KEY\key.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\FWLIB\\src\\misc.c                   0x00000000   Number         0  misc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_gpio.c         0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rcc.c          0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_syscfg.c       0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_usart.c        0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\HARDWARE\\BEEP\\beep.c               0x00000000   Number         0  beep.o ABSOLUTE
    ..\\HARDWARE\\KEY\\key.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\\HARDWARE\\LED\\led.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000198   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000198   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000198   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000198   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x0800019c   Section       36  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x0800019c   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x080001c0   Section        0  memcpya.o(.text)
    .text                                    0x080001e4   Section        0  dmul.o(.text)
    .text                                    0x080002c8   Section        0  f2d.o(.text)
    .text                                    0x080002ee   Section        0  d2f.o(.text)
    .text                                    0x08000326   Section        0  fepilogue.o(.text)
    .text                                    0x08000326   Section        0  iusefp.o(.text)
    .text                                    0x08000394   Section        0  depilogue.o(.text)
    .text                                    0x0800044e   Section        0  dadd.o(.text)
    .text                                    0x0800059c   Section        0  ddiv.o(.text)
    .text                                    0x0800067c   Section       36  init.o(.text)
    .text                                    0x080006a0   Section        0  llshl.o(.text)
    .text                                    0x080006be   Section        0  llushr.o(.text)
    .text                                    0x080006de   Section        0  llsshr.o(.text)
    .text                                    0x08000702   Section        0  dflti.o(.text)
    .text                                    0x08000724   Section        0  dfltui.o(.text)
    .text                                    0x0800073e   Section        0  dfixi.o(.text)
    i.AngleToServoPosition                   0x0800077c   Section        0  usart.o(i.AngleToServoPosition)
    i.BEEP_Init                              0x080007d4   Section        0  beep.o(i.BEEP_Init)
    i.BEEP_Sound                             0x08000810   Section        0  beep.o(i.BEEP_Sound)
    i.BusFault_Handler                       0x08000830   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08000834   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.GPIO_Init                              0x08000836   Section        0  stm32f4xx_gpio.o(i.GPIO_Init)
    i.GPIO_PinAFConfig                       0x080008c6   Section        0  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    i.GPIO_ResetBits                         0x0800090c   Section        0  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08000910   Section        0  stm32f4xx_gpio.o(i.GPIO_SetBits)
    i.HardFault_Handler                      0x08000914   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.LED_Init                               0x08000918   Section        0  led.o(i.LED_Init)
    i.MemManage_Handler                      0x08000958   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x0800095c   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08000960   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x080009d8   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.PendSV_Handler                         0x080009ec   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.Plane_CoordinateInverse                0x080009f0   Section        0  usart.o(i.Plane_CoordinateInverse)
    i.RCC_AHB1PeriphClockCmd                 0x08000abc   Section        0  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    i.RCC_APB1PeriphClockCmd                 0x08000adc   Section        0  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08000afc   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x08000b1c   Section        0  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x08000c04   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.Screen_ProcessCommand                  0x08000c08   Section        0  usart.o(i.Screen_ProcessCommand)
    i.Screen_RequestCoordinateInput          0x080015c0   Section        0  usart.o(i.Screen_RequestCoordinateInput)
    i.Screen_UpdateAngleDisplay              0x0800173c   Section        0  usart.o(i.Screen_UpdateAngleDisplay)
    i.Screen_UpdateCoordinateDisplay         0x08001914   Section        0  usart.o(i.Screen_UpdateCoordinateDisplay)
    i.Servo_AngleFeedbackTest                0x08001bfc   Section        0  usart.o(i.Servo_AngleFeedbackTest)
    i.Servo_CalculateChecksum                0x08001e58   Section        0  usart.o(i.Servo_CalculateChecksum)
    i.Servo_LoadMotor                        0x08001e78   Section        0  usart.o(i.Servo_LoadMotor)
    i.Servo_MoveToCoordinate                 0x08001eb4   Section        0  usart.o(i.Servo_MoveToCoordinate)
    i.Servo_MoveToPosition                   0x080020c0   Section        0  usart.o(i.Servo_MoveToPosition)
    i.Servo_ProcessPacket                    0x08002114   Section        0  usart.o(i.Servo_ProcessPacket)
    i.Servo_ReadPosition                     0x080021a0   Section        0  usart.o(i.Servo_ReadPosition)
    i.Servo_TestPositionAccuracy             0x08002214   Section        0  usart.o(i.Servo_TestPositionAccuracy)
    i.SetSysClock                            0x08002574   Section        0  system_stm32f4xx.o(i.SetSysClock)
    SetSysClock                              0x08002575   Thumb Code   220  system_stm32f4xx.o(i.SetSysClock)
    i.SysTick_CLKSourceConfig                0x08002660   Section        0  misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x08002688   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x0800268c   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.USART1_IRQHandler                      0x080026f4   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x080027cc   Section        0  usart.o(i.USART2_IRQHandler)
    i.USART2_SendArray                       0x0800284c   Section        0  usart.o(i.USART2_SendArray)
    i.USART_Cmd                              0x08002888   Section        0  stm32f4xx_usart.o(i.USART_Cmd)
    i.USART_GetFlagStatus                    0x080028a0   Section        0  stm32f4xx_usart.o(i.USART_GetFlagStatus)
    i.USART_GetITStatus                      0x080028ba   Section        0  stm32f4xx_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x0800290e   Section        0  stm32f4xx_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08002958   Section        0  stm32f4xx_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08002a2c   Section        0  stm32f4xx_usart.o(i.USART_ReceiveData)
    i.USART_SendArray                        0x08002a38   Section        0  usart.o(i.USART_SendArray)
    i.USART_SendData                         0x08002a74   Section        0  stm32f4xx_usart.o(i.USART_SendData)
    i.UsageFault_Handler                     0x08002a7c   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08002a80   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp_atan                          0x08002ab0   Section        0  atan.o(i.__hardfp_atan)
    i.__hardfp_atan2                         0x08002d88   Section        0  atan2.o(i.__hardfp_atan2)
    i.__hardfp_cos                           0x08002f78   Section        0  cos.o(i.__hardfp_cos)
    i.__ieee754_rem_pio2                     0x08003040   Section        0  rred.o(i.__ieee754_rem_pio2)
    i.__kernel_cos                           0x08003478   Section        0  cos_i.o(i.__kernel_cos)
    i.__kernel_poly                          0x080035e8   Section        0  poly.o(i.__kernel_poly)
    i.__kernel_sin                           0x080036e0   Section        0  sin_i.o(i.__kernel_sin)
    i.__mathlib_dbl_infnan                   0x08003810   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_infnan2                  0x08003824   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x08003838   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_underflow                0x08003858   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__scatterload_copy                     0x08003878   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08003886   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08003888   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x08003898   Section        0  errno.o(i.__set_errno)
    i.atan                                   0x080038a4   Section        0  atan.o(i.atan)
    i.delay_init                             0x080038b4   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x080038f0   Section        0  delay.o(i.delay_ms)
    i.delay_xms                              0x08003928   Section        0  delay.o(i.delay_xms)
    i.fabs                                   0x08003974   Section        0  fabs.o(i.fabs)
    i.main                                   0x0800398c   Section        0  main.o(i.main)
    i.uart2_init                             0x08003a58   Section        0  usart.o(i.uart2_init)
    i.uart_init                              0x08003b04   Section        0  usart.o(i.uart_init)
    .constdata                               0x08003bb0   Section       70  usart.o(.constdata)
    .constdata                               0x08003bf8   Section      152  atan.o(.constdata)
    atanhi                                   0x08003bf8   Data          32  atan.o(.constdata)
    atanlo                                   0x08003c18   Data          32  atan.o(.constdata)
    aTodd                                    0x08003c38   Data          40  atan.o(.constdata)
    aTeven                                   0x08003c60   Data          48  atan.o(.constdata)
    .constdata                               0x08003c90   Section       48  cos_i.o(.constdata)
    C                                        0x08003c90   Data          48  cos_i.o(.constdata)
    .constdata                               0x08003cc0   Section        8  qnan.o(.constdata)
    .constdata                               0x08003cc8   Section      200  rred.o(.constdata)
    pio2s                                    0x08003cc8   Data          48  rred.o(.constdata)
    twooverpi                                0x08003cf8   Data         152  rred.o(.constdata)
    .constdata                               0x08003d90   Section       40  sin_i.o(.constdata)
    S                                        0x08003d90   Data          40  sin_i.o(.constdata)
    .conststring                             0x08003db8   Section       15  usart.o(.conststring)
    .data                                    0x20000000   Section        2  main.o(.data)
    display_counter                          0x20000000   Data           2  main.o(.data)
    .data                                    0x20000002   Section        4  delay.o(.data)
    fac_us                                   0x20000002   Data           1  delay.o(.data)
    fac_ms                                   0x20000004   Data           2  delay.o(.data)
    .data                                    0x20000008   Section       34  usart.o(.data)
    Y_coord                                  0x20000020   Data           4  usart.o(.data)
    Z_coord                                  0x20000024   Data           4  usart.o(.data)
    coord_read_state                         0x20000028   Data           1  usart.o(.data)
    square_point_index                       0x20000029   Data           1  usart.o(.data)
    .data                                    0x2000002a   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x2000002a   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x2000003c   Section        4  errno.o(.data)
    _errno                                   0x2000003c   Data           4  errno.o(.data)
    .bss                                     0x20000040   Section      270  usart.o(.bss)
    STACK                                    0x20000150   Section     1024  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __use_no_errno                           0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_exception_handling              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_fp                              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap                            0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap_region                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting_swi                 0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_signal_handling                 0x00000000   Number         0  useno.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000199   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000199   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x0800019d   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI0_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM2_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM3_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM5_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART3_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __aeabi_memcpy                           0x080001c1   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x080001c1   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x080001c1   Thumb Code     0  memcpya.o(.text)
    __aeabi_dmul                             0x080001e5   Thumb Code   228  dmul.o(.text)
    __aeabi_f2d                              0x080002c9   Thumb Code    38  f2d.o(.text)
    __aeabi_d2f                              0x080002ef   Thumb Code    56  d2f.o(.text)
    __I$use$fp                               0x08000327   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08000327   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08000339   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x08000395   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080003b3   Thumb Code   156  depilogue.o(.text)
    __aeabi_dadd                             0x0800044f   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000591   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08000597   Thumb Code     6  dadd.o(.text)
    __aeabi_ddiv                             0x0800059d   Thumb Code   222  ddiv.o(.text)
    __scatterload                            0x0800067d   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x0800067d   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x080006a1   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080006a1   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x080006bf   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x080006bf   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x080006df   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x080006df   Thumb Code     0  llsshr.o(.text)
    __aeabi_i2d                              0x08000703   Thumb Code    34  dflti.o(.text)
    __aeabi_ui2d                             0x08000725   Thumb Code    26  dfltui.o(.text)
    __aeabi_d2iz                             0x0800073f   Thumb Code    62  dfixi.o(.text)
    AngleToServoPosition                     0x0800077d   Thumb Code    66  usart.o(i.AngleToServoPosition)
    BEEP_Init                                0x080007d5   Thumb Code    56  beep.o(i.BEEP_Init)
    BEEP_Sound                               0x08000811   Thumb Code    28  beep.o(i.BEEP_Sound)
    BusFault_Handler                         0x08000831   Thumb Code     4  stm32f4xx_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x08000835   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    GPIO_Init                                0x08000837   Thumb Code   144  stm32f4xx_gpio.o(i.GPIO_Init)
    GPIO_PinAFConfig                         0x080008c7   Thumb Code    70  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    GPIO_ResetBits                           0x0800090d   Thumb Code     4  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08000911   Thumb Code     4  stm32f4xx_gpio.o(i.GPIO_SetBits)
    HardFault_Handler                        0x08000915   Thumb Code     4  stm32f4xx_it.o(i.HardFault_Handler)
    LED_Init                                 0x08000919   Thumb Code    60  led.o(i.LED_Init)
    MemManage_Handler                        0x08000959   Thumb Code     4  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x0800095d   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    NVIC_Init                                0x08000961   Thumb Code   106  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x080009d9   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    PendSV_Handler                           0x080009ed   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    Plane_CoordinateInverse                  0x080009f1   Thumb Code   192  usart.o(i.Plane_CoordinateInverse)
    RCC_AHB1PeriphClockCmd                   0x08000abd   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    RCC_APB1PeriphClockCmd                   0x08000add   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08000afd   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08000b1d   Thumb Code   214  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x08000c05   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    Screen_ProcessCommand                    0x08000c09   Thumb Code  2466  usart.o(i.Screen_ProcessCommand)
    Screen_RequestCoordinateInput            0x080015c1   Thumb Code   378  usart.o(i.Screen_RequestCoordinateInput)
    Screen_UpdateAngleDisplay                0x0800173d   Thumb Code   462  usart.o(i.Screen_UpdateAngleDisplay)
    Screen_UpdateCoordinateDisplay           0x08001915   Thumb Code   742  usart.o(i.Screen_UpdateCoordinateDisplay)
    Servo_AngleFeedbackTest                  0x08001bfd   Thumb Code   598  usart.o(i.Servo_AngleFeedbackTest)
    Servo_CalculateChecksum                  0x08001e59   Thumb Code    32  usart.o(i.Servo_CalculateChecksum)
    Servo_LoadMotor                          0x08001e79   Thumb Code    60  usart.o(i.Servo_LoadMotor)
    Servo_MoveToCoordinate                   0x08001eb5   Thumb Code   492  usart.o(i.Servo_MoveToCoordinate)
    Servo_MoveToPosition                     0x080020c1   Thumb Code    82  usart.o(i.Servo_MoveToPosition)
    Servo_ProcessPacket                      0x08002115   Thumb Code   116  usart.o(i.Servo_ProcessPacket)
    Servo_ReadPosition                       0x080021a1   Thumb Code   106  usart.o(i.Servo_ReadPosition)
    Servo_TestPositionAccuracy               0x08002215   Thumb Code   862  usart.o(i.Servo_TestPositionAccuracy)
    SysTick_CLKSourceConfig                  0x08002661   Thumb Code    40  misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x08002689   Thumb Code     2  stm32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x0800268d   Thumb Code    88  system_stm32f4xx.o(i.SystemInit)
    USART1_IRQHandler                        0x080026f5   Thumb Code   194  usart.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x080027cd   Thumb Code   110  usart.o(i.USART2_IRQHandler)
    USART2_SendArray                         0x0800284d   Thumb Code    56  usart.o(i.USART2_SendArray)
    USART_Cmd                                0x08002889   Thumb Code    24  stm32f4xx_usart.o(i.USART_Cmd)
    USART_GetFlagStatus                      0x080028a1   Thumb Code    26  stm32f4xx_usart.o(i.USART_GetFlagStatus)
    USART_GetITStatus                        0x080028bb   Thumb Code    84  stm32f4xx_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x0800290f   Thumb Code    74  stm32f4xx_usart.o(i.USART_ITConfig)
    USART_Init                               0x08002959   Thumb Code   204  stm32f4xx_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08002a2d   Thumb Code    10  stm32f4xx_usart.o(i.USART_ReceiveData)
    USART_SendArray                          0x08002a39   Thumb Code    56  usart.o(i.USART_SendArray)
    USART_SendData                           0x08002a75   Thumb Code     8  stm32f4xx_usart.o(i.USART_SendData)
    UsageFault_Handler                       0x08002a7d   Thumb Code     4  stm32f4xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08002a81   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_atan                            0x08002ab1   Thumb Code   622  atan.o(i.__hardfp_atan)
    __hardfp_atan2                           0x08002d89   Thumb Code   432  atan2.o(i.__hardfp_atan2)
    __hardfp_cos                             0x08002f79   Thumb Code   180  cos.o(i.__hardfp_cos)
    __ieee754_rem_pio2                       0x08003041   Thumb Code   938  rred.o(i.__ieee754_rem_pio2)
    __kernel_cos                             0x08003479   Thumb Code   322  cos_i.o(i.__kernel_cos)
    __kernel_poly                            0x080035e9   Thumb Code   248  poly.o(i.__kernel_poly)
    __kernel_sin                             0x080036e1   Thumb Code   280  sin_i.o(i.__kernel_sin)
    __mathlib_dbl_infnan                     0x08003811   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_infnan2                    0x08003825   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x08003839   Thumb Code    24  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_underflow                  0x08003859   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    __scatterload_copy                       0x08003879   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08003887   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08003889   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x08003899   Thumb Code     6  errno.o(i.__set_errno)
    atan                                     0x080038a5   Thumb Code    16  atan.o(i.atan)
    delay_init                               0x080038b5   Thumb Code    52  delay.o(i.delay_init)
    delay_ms                                 0x080038f1   Thumb Code    56  delay.o(i.delay_ms)
    delay_xms                                0x08003929   Thumb Code    72  delay.o(i.delay_xms)
    fabs                                     0x08003975   Thumb Code    24  fabs.o(i.fabs)
    main                                     0x0800398d   Thumb Code   190  main.o(i.main)
    uart2_init                               0x08003a59   Thumb Code   162  usart.o(i.uart2_init)
    uart_init                                0x08003b05   Thumb Code   164  usart.o(i.uart_init)
    __mathlib_zero                           0x08003cc0   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x08003dc8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08003de8   Number         0  anon$$obj.o(Region$$Table)
    __stdout                                 0x20000008   Data           4  usart.o(.data)
    USART_RX_STA                             0x2000000c   Data           2  usart.o(.data)
    SERVO_RX_STA                             0x2000000e   Data           1  usart.o(.data)
    SERVO_RX_CNT                             0x2000000f   Data           1  usart.o(.data)
    SERVO_PKT_LEN                            0x20000010   Data           1  usart.o(.data)
    SCREEN_RX_STA                            0x20000011   Data           1  usart.o(.data)
    SCREEN_RX_CNT                            0x20000012   Data           1  usart.o(.data)
    SERVO1_POSITION                          0x20000014   Data           2  usart.o(.data)
    SERVO2_POSITION                          0x20000016   Data           2  usart.o(.data)
    auto_refresh_enabled                     0x20000018   Data           1  usart.o(.data)
    servo_feedback_position                  0x2000001a   Data           2  usart.o(.data)
    servo_feedback_received                  0x2000001c   Data           1  usart.o(.data)
    USART_RX_BUF                             0x20000040   Data         200  usart.o(.bss)
    SERVO_RX_BUF                             0x20000108   Data          20  usart.o(.bss)
    SCREEN_RX_BUF                            0x2000011c   Data          50  usart.o(.bss)
    __initial_sp                             0x20000550   Data           0  startup_stm32f40_41xxx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00003e28, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00003de8, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO          588    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000000   Code   RO         1337  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO         1399    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         1402    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO         1404    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         1406    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         1407    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000000   Code   RO         1409    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000198   0x08000198   0x00000000   Code   RO         1411    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         1400    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x0800019c   0x0800019c   0x00000024   Code   RO          589    .text               startup_stm32f40_41xxx.o
    0x080001c0   0x080001c0   0x00000024   Code   RO         1340    .text               mc_w.l(memcpya.o)
    0x080001e4   0x080001e4   0x000000e4   Code   RO         1344    .text               mf_w.l(dmul.o)
    0x080002c8   0x080002c8   0x00000026   Code   RO         1346    .text               mf_w.l(f2d.o)
    0x080002ee   0x080002ee   0x00000038   Code   RO         1348    .text               mf_w.l(d2f.o)
    0x08000326   0x08000326   0x00000000   Code   RO         1420    .text               mc_w.l(iusefp.o)
    0x08000326   0x08000326   0x0000006e   Code   RO         1421    .text               mf_w.l(fepilogue.o)
    0x08000394   0x08000394   0x000000ba   Code   RO         1423    .text               mf_w.l(depilogue.o)
    0x0800044e   0x0800044e   0x0000014e   Code   RO         1425    .text               mf_w.l(dadd.o)
    0x0800059c   0x0800059c   0x000000de   Code   RO         1427    .text               mf_w.l(ddiv.o)
    0x0800067a   0x0800067a   0x00000002   PAD
    0x0800067c   0x0800067c   0x00000024   Code   RO         1435    .text               mc_w.l(init.o)
    0x080006a0   0x080006a0   0x0000001e   Code   RO         1437    .text               mc_w.l(llshl.o)
    0x080006be   0x080006be   0x00000020   Code   RO         1439    .text               mc_w.l(llushr.o)
    0x080006de   0x080006de   0x00000024   Code   RO         1441    .text               mc_w.l(llsshr.o)
    0x08000702   0x08000702   0x00000022   Code   RO         1443    .text               mf_w.l(dflti.o)
    0x08000724   0x08000724   0x0000001a   Code   RO         1445    .text               mf_w.l(dfltui.o)
    0x0800073e   0x0800073e   0x0000003e   Code   RO         1447    .text               mf_w.l(dfixi.o)
    0x0800077c   0x0800077c   0x00000058   Code   RO          423    i.AngleToServoPosition  usart.o
    0x080007d4   0x080007d4   0x0000003c   Code   RO          309    i.BEEP_Init         beep.o
    0x08000810   0x08000810   0x00000020   Code   RO          310    i.BEEP_Sound        beep.o
    0x08000830   0x08000830   0x00000004   Code   RO          155    i.BusFault_Handler  stm32f4xx_it.o
    0x08000834   0x08000834   0x00000002   Code   RO          156    i.DebugMon_Handler  stm32f4xx_it.o
    0x08000836   0x08000836   0x00000090   Code   RO          640    i.GPIO_Init         stm32f4xx_gpio.o
    0x080008c6   0x080008c6   0x00000046   Code   RO          641    i.GPIO_PinAFConfig  stm32f4xx_gpio.o
    0x0800090c   0x0800090c   0x00000004   Code   RO          647    i.GPIO_ResetBits    stm32f4xx_gpio.o
    0x08000910   0x08000910   0x00000004   Code   RO          648    i.GPIO_SetBits      stm32f4xx_gpio.o
    0x08000914   0x08000914   0x00000004   Code   RO          157    i.HardFault_Handler  stm32f4xx_it.o
    0x08000918   0x08000918   0x00000040   Code   RO          289    i.LED_Init          led.o
    0x08000958   0x08000958   0x00000004   Code   RO          158    i.MemManage_Handler  stm32f4xx_it.o
    0x0800095c   0x0800095c   0x00000002   Code   RO          159    i.NMI_Handler       stm32f4xx_it.o
    0x0800095e   0x0800095e   0x00000002   PAD
    0x08000960   0x08000960   0x00000078   Code   RO          595    i.NVIC_Init         misc.o
    0x080009d8   0x080009d8   0x00000014   Code   RO          596    i.NVIC_PriorityGroupConfig  misc.o
    0x080009ec   0x080009ec   0x00000002   Code   RO          160    i.PendSV_Handler    stm32f4xx_it.o
    0x080009ee   0x080009ee   0x00000002   PAD
    0x080009f0   0x080009f0   0x000000cc   Code   RO          424    i.Plane_CoordinateInverse  usart.o
    0x08000abc   0x08000abc   0x00000020   Code   RO          737    i.RCC_AHB1PeriphClockCmd  stm32f4xx_rcc.o
    0x08000adc   0x08000adc   0x00000020   Code   RO          746    i.RCC_APB1PeriphClockCmd  stm32f4xx_rcc.o
    0x08000afc   0x08000afc   0x00000020   Code   RO          749    i.RCC_APB2PeriphClockCmd  stm32f4xx_rcc.o
    0x08000b1c   0x08000b1c   0x000000e8   Code   RO          758    i.RCC_GetClocksFreq  stm32f4xx_rcc.o
    0x08000c04   0x08000c04   0x00000002   Code   RO          161    i.SVC_Handler       stm32f4xx_it.o
    0x08000c06   0x08000c06   0x00000002   PAD
    0x08000c08   0x08000c08   0x000009b8   Code   RO          425    i.Screen_ProcessCommand  usart.o
    0x080015c0   0x080015c0   0x0000017a   Code   RO          426    i.Screen_RequestCoordinateInput  usart.o
    0x0800173a   0x0800173a   0x00000002   PAD
    0x0800173c   0x0800173c   0x000001d8   Code   RO          427    i.Screen_UpdateAngleDisplay  usart.o
    0x08001914   0x08001914   0x000002e6   Code   RO          428    i.Screen_UpdateCoordinateDisplay  usart.o
    0x08001bfa   0x08001bfa   0x00000002   PAD
    0x08001bfc   0x08001bfc   0x0000025c   Code   RO          429    i.Servo_AngleFeedbackTest  usart.o
    0x08001e58   0x08001e58   0x00000020   Code   RO          430    i.Servo_CalculateChecksum  usart.o
    0x08001e78   0x08001e78   0x0000003c   Code   RO          431    i.Servo_LoadMotor   usart.o
    0x08001eb4   0x08001eb4   0x0000020c   Code   RO          432    i.Servo_MoveToCoordinate  usart.o
    0x080020c0   0x080020c0   0x00000052   Code   RO          433    i.Servo_MoveToPosition  usart.o
    0x08002112   0x08002112   0x00000002   PAD
    0x08002114   0x08002114   0x0000008c   Code   RO          434    i.Servo_ProcessPacket  usart.o
    0x080021a0   0x080021a0   0x00000074   Code   RO          435    i.Servo_ReadPosition  usart.o
    0x08002214   0x08002214   0x0000035e   Code   RO          437    i.Servo_TestPositionAccuracy  usart.o
    0x08002572   0x08002572   0x00000002   PAD
    0x08002574   0x08002574   0x000000ec   Code   RO          252    i.SetSysClock       system_stm32f4xx.o
    0x08002660   0x08002660   0x00000028   Code   RO          599    i.SysTick_CLKSourceConfig  misc.o
    0x08002688   0x08002688   0x00000002   Code   RO          162    i.SysTick_Handler   stm32f4xx_it.o
    0x0800268a   0x0800268a   0x00000002   PAD
    0x0800268c   0x0800268c   0x00000068   Code   RO          254    i.SystemInit        system_stm32f4xx.o
    0x080026f4   0x080026f4   0x000000d8   Code   RO          438    i.USART1_IRQHandler  usart.o
    0x080027cc   0x080027cc   0x00000080   Code   RO          439    i.USART2_IRQHandler  usart.o
    0x0800284c   0x0800284c   0x0000003c   Code   RO          440    i.USART2_SendArray  usart.o
    0x08002888   0x08002888   0x00000018   Code   RO         1131    i.USART_Cmd         stm32f4xx_usart.o
    0x080028a0   0x080028a0   0x0000001a   Code   RO         1134    i.USART_GetFlagStatus  stm32f4xx_usart.o
    0x080028ba   0x080028ba   0x00000054   Code   RO         1135    i.USART_GetITStatus  stm32f4xx_usart.o
    0x0800290e   0x0800290e   0x0000004a   Code   RO         1137    i.USART_ITConfig    stm32f4xx_usart.o
    0x08002958   0x08002958   0x000000d4   Code   RO         1138    i.USART_Init        stm32f4xx_usart.o
    0x08002a2c   0x08002a2c   0x0000000a   Code   RO         1145    i.USART_ReceiveData  stm32f4xx_usart.o
    0x08002a36   0x08002a36   0x00000002   PAD
    0x08002a38   0x08002a38   0x0000003c   Code   RO          441    i.USART_SendArray   usart.o
    0x08002a74   0x08002a74   0x00000008   Code   RO         1148    i.USART_SendData    stm32f4xx_usart.o
    0x08002a7c   0x08002a7c   0x00000004   Code   RO          163    i.UsageFault_Handler  stm32f4xx_it.o
    0x08002a80   0x08002a80   0x00000030   Code   RO         1431    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08002ab0   0x08002ab0   0x000002d8   Code   RO         1350    i.__hardfp_atan     m_wm.l(atan.o)
    0x08002d88   0x08002d88   0x000001f0   Code   RO         1313    i.__hardfp_atan2    m_wm.l(atan2.o)
    0x08002f78   0x08002f78   0x000000c8   Code   RO         1325    i.__hardfp_cos      m_wm.l(cos.o)
    0x08003040   0x08003040   0x00000438   Code   RO         1388    i.__ieee754_rem_pio2  m_wm.l(rred.o)
    0x08003478   0x08003478   0x00000170   Code   RO         1364    i.__kernel_cos      m_wm.l(cos_i.o)
    0x080035e8   0x080035e8   0x000000f8   Code   RO         1433    i.__kernel_poly     m_wm.l(poly.o)
    0x080036e0   0x080036e0   0x00000130   Code   RO         1393    i.__kernel_sin      m_wm.l(sin_i.o)
    0x08003810   0x08003810   0x00000014   Code   RO         1368    i.__mathlib_dbl_infnan  m_wm.l(dunder.o)
    0x08003824   0x08003824   0x00000014   Code   RO         1369    i.__mathlib_dbl_infnan2  m_wm.l(dunder.o)
    0x08003838   0x08003838   0x00000020   Code   RO         1370    i.__mathlib_dbl_invalid  m_wm.l(dunder.o)
    0x08003858   0x08003858   0x00000020   Code   RO         1373    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x08003878   0x08003878   0x0000000e   Code   RO         1451    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08003886   0x08003886   0x00000002   Code   RO         1452    i.__scatterload_null  mc_w.l(handlers.o)
    0x08003888   0x08003888   0x0000000e   Code   RO         1453    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08003896   0x08003896   0x00000002   PAD
    0x08003898   0x08003898   0x0000000c   Code   RO         1415    i.__set_errno       mc_w.l(errno.o)
    0x080038a4   0x080038a4   0x00000010   Code   RO         1352    i.atan              m_wm.l(atan.o)
    0x080038b4   0x080038b4   0x0000003c   Code   RO          365    i.delay_init        delay.o
    0x080038f0   0x080038f0   0x00000038   Code   RO          366    i.delay_ms          delay.o
    0x08003928   0x08003928   0x0000004c   Code   RO          368    i.delay_xms         delay.o
    0x08003974   0x08003974   0x00000018   Code   RO         1383    i.fabs              m_wm.l(fabs.o)
    0x0800398c   0x0800398c   0x000000cc   Code   RO            3    i.main              main.o
    0x08003a58   0x08003a58   0x000000ac   Code   RO          444    i.uart2_init        usart.o
    0x08003b04   0x08003b04   0x000000ac   Code   RO          445    i.uart_init         usart.o
    0x08003bb0   0x08003bb0   0x00000046   Data   RO          447    .constdata          usart.o
    0x08003bf6   0x08003bf6   0x00000002   PAD
    0x08003bf8   0x08003bf8   0x00000098   Data   RO         1353    .constdata          m_wm.l(atan.o)
    0x08003c90   0x08003c90   0x00000030   Data   RO         1365    .constdata          m_wm.l(cos_i.o)
    0x08003cc0   0x08003cc0   0x00000008   Data   RO         1387    .constdata          m_wm.l(qnan.o)
    0x08003cc8   0x08003cc8   0x000000c8   Data   RO         1390    .constdata          m_wm.l(rred.o)
    0x08003d90   0x08003d90   0x00000028   Data   RO         1394    .constdata          m_wm.l(sin_i.o)
    0x08003db8   0x08003db8   0x0000000f   Data   RO          448    .conststring        usart.o
    0x08003dc7   0x08003dc7   0x00000001   PAD
    0x08003dc8   0x08003dc8   0x00000020   Data   RO         1449    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08003de8, Size: 0x00000550, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08003de8   0x00000002   Data   RW            4    .data               main.o
    0x20000002   0x08003dea   0x00000004   Data   RW          369    .data               delay.o
    0x20000006   0x08003dee   0x00000002   PAD
    0x20000008   0x08003df0   0x00000022   Data   RW          449    .data               usart.o
    0x2000002a   0x08003e12   0x00000010   Data   RW          790    .data               stm32f4xx_rcc.o
    0x2000003a   0x08003e22   0x00000002   PAD
    0x2000003c   0x08003e24   0x00000004   Data   RW         1416    .data               mc_w.l(errno.o)
    0x20000040        -       0x0000010e   Zero   RW          446    .bss                usart.o
    0x2000014e   0x08003e28   0x00000002   PAD
    0x20000150        -       0x00000400   Zero   RW          586    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        92          8          0          0          0       1145   beep.o
       192         12          0          4          0       2080   delay.o
        64          4          0          0          0        563   led.o
       204         14          0          2          0     262667   main.o
       180         24          0          0          0       2193   misc.o
        36          8        392          0       1024        848   startup_stm32f40_41xxx.o
       222          0          0          0          0       3023   stm32f4xx_gpio.o
        26          0          0          0          0       4334   stm32f4xx_it.o
       328         36          0         16          0       4799   stm32f4xx_rcc.o
       438          8          0          0          0       5697   stm32f4xx_usart.o
       340         32          0          0          0       1561   system_stm32f4xx.o
      7600        258         85         34        270      21570   usart.o

    ----------------------------------------------------------------------
      9740        <USER>        <GROUP>         60       1296     310480   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        18          0          3          4          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       744        106        152          0          0        352   atan.o
       496         64          0          0          0        192   atan2.o
       200         20          0          0          0        164   cos.o
       368         46         48          0          0        200   cos_i.o
       104         16          0          0          0        496   dunder.o
        24          0          0          0          0        124   fabs.o
        48          0          0          0          0        124   fpclassify.o
       248          0          0          0          0        152   poly.o
         0          0          8          0          0          0   qnan.o
      1080        142        200          0          0        188   rred.o
       304         24         40          0          0        208   sin_i.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         68   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        62          0          0          0          0         80   dfixi.o
        34          0          0          0          0         76   dflti.o
        26          0          0          0          0         76   dfltui.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o

    ----------------------------------------------------------------------
      5148        <USER>        <GROUP>          4          0       3684   Library Totals
         4          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      3616        418        448          0          0       2200   m_wm.l
       232         22          0          4          0        408   mc_w.l
      1296          0          0          0          0       1076   mf_w.l

    ----------------------------------------------------------------------
      5148        <USER>        <GROUP>          4          0       3684   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     14888        844        960         64       1296     307292   Grand Totals
     14888        844        960         64       1296     307292   ELF Image Totals
     14888        844        960         64          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                15848 (  15.48kB)
    Total RW  Size (RW Data + ZI Data)              1360 (   1.33kB)
    Total ROM Size (Code + RO Data + RW Data)      15912 (  15.54kB)

==============================================================================

