#ifndef __USART_H
#define __USART_H
#include "stdio.h"	
#include "stm32f4xx_conf.h"
#include "sys.h" 
#include "math.h" 
//////////////////////////////////////////////////////////////////////////////////	 
//������ֻ��ѧϰʹ�ã�δ���������ɣ��������������κ���;
//Mini STM32������
//����1��ʼ��		   
//����ԭ��@ALIENTEK
//������̳:www.openedv.csom
//�޸�����:2011/6/14
//�汾��V1.4
//��Ȩ���У�����ؾ���
//Copyright(C) ����ԭ�� 2009-2019
//All rights reserved
//********************************************************************************
//V1.3�޸�˵�� 
//֧����Ӧ��ͬƵ���µĴ��ڲ���������.
//�����˶�printf��֧��
//�����˴��ڽ��������.
//������printf��һ���ַ���ʧ��bug
//V1.4�޸�˵��
//1,�޸Ĵ��ڳ�ʼ��IO��bug
//2,�޸���USART_RX_STA,ʹ�ô����������ֽ���Ϊ2��14�η�
//3,������USART_REC_LEN,���ڶ��崮������������յ��ֽ���(������2��14�η�)
//4,�޸���EN_USART1_RX��ʹ�ܷ�ʽ
////////////////////////////////////////////////////////////////////////////////// 	
#define USART_REC_LEN  			200  	//定义最大接收字节数 200
#define EN_USART1_RX 			1		//使能（1）/禁止（0）串口1接收

// 舵机协议相关定义
#define SERVO_FRAME_HEADER		0x55	//舵机数据包帧头
#define SERVO_MAX_PACKET_LEN	20		//舵机数据包最大长度
	  	
extern u8  USART_RX_BUF[USART_REC_LEN]; //接收缓冲,最大USART_REC_LEN个字节.末字节为换行符 
extern u16 USART_RX_STA;         		//接收状态标记
extern u8  SERVO_RX_BUF[SERVO_MAX_PACKET_LEN]; //舵机接收缓冲
extern u8  SERVO_RX_STA;				//舵机接收状态: 0-等待帧头1, 1-等待帧头2, 2-接收数据
extern u8  SERVO_RX_CNT;				//舵机接收计数器
extern u8  SERVO_PKT_LEN;				//舵机数据包长度	
//串口相关函数声明，请不要注释下面这句话
void uart_init(u32 bound);

// 舵机控制函数声明
void USART_SendArray(u8 *arr, u16 len);
u8 Servo_CalculateChecksum(u8 *data, u8 len);
void Servo_SetMotorMode(u8 id, s16 speed);
void Servo_LoadMotor(u8 id);
void Servo_MoveToPosition(u8 id, u16 position, u16 time);
u8 Servo_ProcessPacket(void);

// UART2串口屏通信相关定义
#define EN_USART2_RX                   1               //使能（1）/禁止（0）串口2接收
#define SCREEN_REC_LEN                 50              //串口屏接收缓冲长度

extern u8  SCREEN_RX_BUF[SCREEN_REC_LEN];             //串口屏接收缓冲
extern u8  SCREEN_RX_STA;                             //串口屏接收状态
extern u8  SCREEN_RX_CNT;                             //串口屏接收计数

// 舵机位置跟踪变量
extern u16 SERVO1_POSITION;                          //舵机1当前位置
extern u16 SERVO2_POSITION;                          //舵机2当前位置

// UART2串口屏通信函数声明
void uart2_init(u32 bound);
void USART2_SendArray(u8 *arr, u16 len);
void Screen_ProcessCommand(void);
void Screen_UpdateAngleDisplay(void);

// 平面坐标逆解相关定义
#define PI 3.14159265359f
#define DISTANCE_TO_PLANE 500.0f  // 云台到A4纸距离(mm)

// 激光器偏移补偿参数（需要通过实测校准确定）
#define LASER_OFFSET_Y  0.0f    // Y轴偏移量(mm)，正值表示激光器偏右
#define LASER_OFFSET_Z  0.0f    // Z轴偏移量(mm)，正值表示激光器偏上

// 分轴距离系数（用于校正几何精度）
#define DISTANCE_Y_AXIS  500.0f   // Y轴距离系数
#define DISTANCE_Z_AXIS  500.0f   // Z轴距离系数

// 高精度校准参数
#define ENABLE_ADVANCED_CALIBRATION  1    // 启用高级校准
#define CALIBRATION_POINTS_COUNT     9    // 校准点数量(3x3网格)

// 非线性补偿参数
#define Y_AXIS_NONLINEAR_FACTOR  1.0f    // Y轴非线性补偿系数
#define Z_AXIS_NONLINEAR_FACTOR  1.0f    // Z轴非线性补偿系数

// 平面坐标逆解函数声明
void Plane_CoordinateInverse(float Y, float Z, float *theta, float *phi);
u16 AngleToServoPosition(float angle_rad);
void Servo_MoveToCoordinate(float Y, float Z);
void Screen_RequestCoordinateInput(void);
void Screen_UpdateCoordinateDisplay(float Y, float Z, float theta_deg, float phi_deg);

// 舵机角度反馈测试函数声明
u16 Servo_ReadPosition(u8 servo_id);
void Servo_AngleFeedbackTest(void);
void Servo_TestPositionAccuracy(u8 servo_id, u16 target_pos);

// 舵机角度反馈测试全局变量声明
extern volatile u16 servo_feedback_position;
extern volatile u8 servo_feedback_received;

// 校准数据结构
typedef struct {
    float target_Y;        // 目标Y坐标
    float target_Z;        // 目标Z坐标
    float actual_Y;        // 实际Y坐标
    float actual_Z;        // 实际Z坐标
    float error_Y;         // Y轴误差
    float error_Z;         // Z轴误差
} CalibrationPoint_t;

// 校准函数声明
void Calibration_Start(void);
void Calibration_AddPoint(float target_Y, float target_Z, float actual_Y, float actual_Z);
void Calibration_Calculate(void);
void Coordinate_ApplyCompensation(float *Y, float *Z);

// 角度显示控制变量
extern u8 auto_refresh_enabled;  // 自动刷新使能标志

#endif


