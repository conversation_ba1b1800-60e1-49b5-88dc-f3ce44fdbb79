# 增强版白色四边形卡片识别程序
# 结合色块检测和矩形检测，基于博主教学代码和官方例程改造

import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms

sensor = None

try:
    print("增强版白色四边形卡片识别程序启动")

    # 基于博主教学代码的摄像头配置
    sensor = Sensor(width=1024, height=768)
    sensor.reset()

    # 设置摄像头参数
    sensor.set_framesize(width=1024, height=768)
    sensor.set_pixformat(Sensor.RGB565)

    Display.init(Display.LT9611, to_ide=True)
    # 初始化媒体管理器
    MediaManager.init()
    # 启动 sensor
    sensor.run()
    clock = time.clock()

    while True:
        clock.tick()
        os.exitpoint()
        img = sensor.snapshot(chn=CAM_CHN_ID_0)

        # 方法1: 基于博主05色块追踪代码的色块检测
        # 检测白色色块（LAB颜色空间）
        white_blobs = img.find_blobs([(41, 100, -20, 20, -20, 20)], False,
                                   (0, 0, 1024, 768), x_stride=5, y_stride=5, 
                                   pixels_threshold=5000, margin=True)
        
        if white_blobs:
            print(f"检测到 {len(white_blobs)} 个白色区域")
            
            # 选择最大的白色区域
            largest_blob = max(white_blobs, key=lambda b: b.pixels())
            
            # 绘制白色区域边界
            img.draw_rectangle(largest_blob.x(), largest_blob.y(), 
                             largest_blob.w(), largest_blob.h(), 
                             color=(255, 255, 0), thickness=3, fill=False)
            
            # 在白色区域内进行矩形检测
            blob_roi = (largest_blob.x(), largest_blob.y(), largest_blob.w(), largest_blob.h())
            
            # 方法2: 基于04矩形识别代码的矩形检测
            img_rect = img.to_grayscale(copy=True)
            img_rect = img_rect.binary([(82, 212)])
            
            # 在色块ROI内检测矩形
            rects = img_rect.find_rects(roi=blob_roi, threshold=8000)
            
            if rects is not None and len(rects) > 0:
                print(f"在白色区域内检测到 {len(rects)} 个矩形")
                
                for i, rect in enumerate(rects):
                    # 获取四个角点坐标
                    corner = rect.corners()
                    
                    # 将ROI内坐标转换为全图坐标
                    global_corners = [(corner[j][0] + largest_blob.x(), 
                                     corner[j][1] + largest_blob.y()) for j in range(4)]
                    
                    # 基于04矩形识别代码的绘制方法
                    img.draw_line(global_corners[0][0], global_corners[0][1], 
                                global_corners[1][0], global_corners[1][1], 
                                color=(0, 255, 0), thickness=5)
                    img.draw_line(global_corners[2][0], global_corners[2][1], 
                                global_corners[1][0], global_corners[1][1], 
                                color=(0, 255, 0), thickness=5)
                    img.draw_line(global_corners[2][0], global_corners[2][1], 
                                global_corners[3][0], global_corners[3][1], 
                                color=(0, 255, 0), thickness=5)
                    img.draw_line(global_corners[0][0], global_corners[0][1], 
                                global_corners[3][0], global_corners[3][1], 
                                color=(0, 255, 0), thickness=5)
                    
                    # 绘制角点
                    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)]
                    for j, point in enumerate(global_corners):
                        img.draw_circle(point[0], point[1], 8, color=colors[j], thickness=3)
                    
                    # 输出顶点坐标
                    print(f"白色卡片{i+1}的四个顶点坐标:")
                    corner_names = ["角点1", "角点2", "角点3", "角点4"]
                    for j, point in enumerate(global_corners):
                        print(f"  {corner_names[j]}: ({point[0]}, {point[1]})")
                    
                    # 基于12_PID激光点回中代码的中心点计算方法
                    center_x = sum([global_corners[k][0] for k in range(4)]) / 4
                    center_y = sum([global_corners[k][1] for k in range(4)]) / 4
                    img.draw_circle(int(center_x), int(center_y), 10, 
                                  color=(255, 0, 255), thickness=3, fill=True)
                    print(f"  中心点: ({int(center_x)}, {int(center_y)})")
                    print("---")
            else:
                print("在白色区域内未检测到矩形")
        else:
            print("未检测到白色区域")

        # 基于博主教学代码的信息显示
        img.draw_string_advanced(50, 50, 60, "fps: {}".format(clock.fps()), color=(255, 0, 0))
        img.draw_string_advanced(50, 120, 40, "White Card Detection", color=(0, 255, 0))
        
        img.compressed_for_ide()
        Display.show_image(img)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    # 基于博主教学代码的清理流程
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()