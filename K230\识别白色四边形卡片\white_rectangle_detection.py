# 基于庐山派K230的白色四边形卡片识别程序
# 基于官方例程和博主教学代码改造
# 功能: 识别A4纸大小的白色四边形卡片，获取四个顶点的像素坐标

import time, os, gc, sys, math

from media.sensor import *
from media.display import *
from media.media import *

DETECT_WIDTH = 640
DETECT_HEIGHT = 480

# 白色卡片检测阈值 - LAB颜色空间 (根据实际环境调节)
WHITE_THRESHOLD = [(51, 76, -22, 35, -25, 34)]

sensor = None

def camera_init():
    global sensor
    # 基于博主教学代码的摄像头初始化
    sensor = Sensor(width=DETECT_WIDTH, height=DETECT_HEIGHT)
    sensor.reset()
    
    # 设置帧大小和像素格式
    sensor.set_framesize(width=DETECT_WIDTH, height=DETECT_HEIGHT)
    sensor.set_pixformat(Sensor.RGB565)

    # 使用IDE作为显示输出
    Display.init(Display.VIRT, width=DETECT_WIDTH, height=DETECT_HEIGHT, fps=100, to_ide=True)
    # 初始化媒体管理器
    MediaManager.init()
    # 启动传感器
    sensor.run()

def camera_deinit():
    global sensor
    # 基于官方例程的清理代码
    sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()

def point_in_polygon(point, polygon):
    """
    检查点是否在多边形内（射线法）
    基于A4纸检测代码中的算法
    """
    x, y = point
    n = len(polygon)
    inside = False
    
    p1x, p1y = polygon[0]
    for i in range(1, n + 1):
        p2x, p2y = polygon[i % n]
        if y > min(p1y, p2y):
            if y <= max(p1y, p2y):
                if x <= max(p1x, p2x):
                    if p1y != p2y:
                        xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                    if p1x == p2x or x <= xinters:
                        inside = not inside
        p1x, p1y = p2x, p2y
    
    return inside

def detect_white_rectangles():
    """
    主检测函数，结合多种方法检测白色四边形卡片
    """
    fps = time.clock()
    while True:
        fps.tick()
        try:
            os.exitpoint()
            global sensor
            img = sensor.snapshot()

            # 计算画面中心坐标
            center_x, center_y = DETECT_WIDTH // 2, DETECT_HEIGHT // 2
            
            # 绘制画面中心标记
            img.draw_circle(center_x, center_y, 5, color=(255, 0, 255), thickness=2)
            
            # 方法1: 基于A4纸检测代码的色块检测
            blobs = img.find_blobs(WHITE_THRESHOLD, pixels_threshold=3000, area_threshold=3000, merge=True)
            
            print(f"检测到 {len(blobs) if blobs else 0} 个白色区域")
            
            if blobs:
                # 选择最大的白色区域
                largest_blob = max(blobs, key=lambda b: b.pixels())
                
                # 绘制最大白色区域的边界框
                img.draw_rectangle([v for v in largest_blob.rect()], color=(255, 255, 0), thickness=2)
                
                # 在白色区域内进行矩形检测
                x, y, w, h = largest_blob.rect()
                roi = (x, y, w, h)
                
                # 方法2: 基于博主教学代码的矩形识别
                # 创建灰度图像副本进行处理
                img_rect = img.to_grayscale(copy=True)
                # 二值化处理 - 基于04矩形识别代码的参数
                img_rect = img_rect.binary([(82, 212)])
                
                # 在ROI内进行矩形检测 - 基于官方find_rects例程
                rects = img_rect.find_rects(roi=roi, threshold=8000)
                
                print(f"在白色区域内检测到 {len(rects) if rects else 0} 个矩形")
                
                if rects:
                    # 寻找最合适的四边形（面积最大或包含中心点）
                    best_rect = None
                    best_score = 0
                    
                    for rect in rects:
                        corners = rect.corners()
                        # 将ROI内的坐标转换为全图坐标
                        global_corners = [(corner[0] + x, corner[1] + y) for corner in corners]
                        
                        # 计算矩形面积作为评分
                        rect_area = w * h
                        score = rect_area
                        
                        # 如果包含画面中心，增加评分
                        if point_in_polygon((center_x, center_y), global_corners):
                            score += 50000
                            
                        if score > best_score:
                            best_score = score
                            best_rect = rect
                    
                    if best_rect:
                        # 获取最佳矩形的角点坐标并转换为全图坐标
                        corners = best_rect.corners()
                        global_corners = [(corner[0] + x, corner[1] + y) for corner in corners]
                        
                        # 基于04矩形识别代码的绘制方法
                        # 绘制四边形的四条边
                        img.draw_line(global_corners[0][0], global_corners[0][1], 
                                    global_corners[1][0], global_corners[1][1], 
                                    color=(0, 255, 0), thickness=3)
                        img.draw_line(global_corners[2][0], global_corners[2][1], 
                                    global_corners[1][0], global_corners[1][1], 
                                    color=(0, 255, 0), thickness=3)
                        img.draw_line(global_corners[2][0], global_corners[2][1], 
                                    global_corners[3][0], global_corners[3][1], 
                                    color=(0, 255, 0), thickness=3)
                        img.draw_line(global_corners[0][0], global_corners[0][1], 
                                    global_corners[3][0], global_corners[3][1], 
                                    color=(0, 255, 0), thickness=3)
                        
                        # 绘制四个角点，使用不同颜色标识
                        colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)]
                        for i, corner in enumerate(global_corners):
                            img.draw_circle(corner[0], corner[1], 6, color=colors[i], thickness=2)
                        
                        # 输出四个顶点的像素坐标
                        print("检测到白色四边形卡片，四个顶点坐标:")
                        corner_names = ["左上角", "右上角", "右下角", "左下角"]
                        for i, corner in enumerate(global_corners):
                            print(f"  {corner_names[i]}: ({corner[0]}, {corner[1]})")
                        
                        # 计算并显示中心点
                        center_x_rect = sum([corner[0] for corner in global_corners]) / 4
                        center_y_rect = sum([corner[1] for corner in global_corners]) / 4
                        img.draw_circle(int(center_x_rect), int(center_y_rect), 4, 
                                      color=(255, 0, 255), thickness=2, fill=True)
                        print(f"  中心点: ({int(center_x_rect)}, {int(center_y_rect)})")
                        print("---")
                    else:
                        print("未找到合适的四边形")
                else:
                    print("未检测到矩形")
            else:
                print("未检测到白色区域")

            # 基于博主教学代码的FPS显示
            img.draw_string_advanced(10, 10, 30, f"FPS: {fps.fps():.1f}", color=(255, 0, 0))
            img.draw_string_advanced(10, 50, 20, "White Rectangle Detection", color=(0, 255, 0))
            
            # 显示结果
            Display.show_image(img)
            img = None

            gc.collect()
            
        except KeyboardInterrupt as e:
            print("用户停止: ", e)
            break
        except BaseException as e:
            print(f"异常: {e}")
            break

def main():
    """
    主函数 - 基于官方例程的标准结构
    """
    os.exitpoint(os.EXITPOINT_ENABLE)
    camera_is_init = False
    try:
        print("开始初始化摄像头...")
        camera_init()
        camera_is_init = True
        print("摄像头初始化完成")
        print("开始白色四边形卡片检测...")
        print("请将白色四边形卡片放在摄像头前")
        detect_white_rectangles()
    except Exception as e:
        print(f"程序异常: {e}")
    finally:
        if camera_is_init:
            print("正在清理资源...")
            camera_deinit()
            print("程序结束")

if __name__ == "__main__":
    main()