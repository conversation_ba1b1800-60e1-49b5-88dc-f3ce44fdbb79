#include "sys.h"
#include "usart.h"
#include "delay.h"
#include "led.h"
#include "beep.h"
////////////////////////////////////////////////////////////////////////////////// 	 
//���ʹ��ucos,����������ͷ�ļ�����.
#if SYSTEM_SUPPORT_OS
#include "includes.h"					//ucos ʹ��	  
#endif
//////////////////////////////////////////////////////////////////////////////////	 
//������ֻ��ѧϰʹ�ã�δ���������ɣ��������������κ���;
//ALIENTEK STM32F4̽���߿�����
//����1��ʼ��		   
//����ԭ��@ALIENTEK
//������̳:www.openedv.com
//�޸�����:2014/6/10
//�汾��V1.5
//��Ȩ���У�����ؾ���
//Copyright(C) �������������ӿƼ����޹�˾ 2009-2019
//All rights reserved
//********************************************************************************
//V1.3�޸�˵�� 
//֧����Ӧ��ͬƵ���µĴ��ڲ���������.
//�����˶�printf��֧��
//�����˴��ڽ��������.
//������printf��һ���ַ���ʧ��bug
//V1.4�޸�˵��
//1,�޸Ĵ��ڳ�ʼ��IO��bug
//2,�޸���USART_RX_STA,ʹ�ô����������ֽ���Ϊ2��14�η�
//3,������USART_REC_LEN,���ڶ��崮������������յ��ֽ���(������2��14�η�)
//4,�޸���EN_USART1_RX��ʹ�ܷ�ʽ
//V1.5�޸�˵��
//1,�����˶�UCOSII��֧��
////////////////////////////////////////////////////////////////////////////////// 	  
 

//////////////////////////////////////////////////////////////////
//�������´���,֧��printf����,������Ҫѡ��use MicroLIB	  
#if 1
#pragma import(__use_no_semihosting)             
//��׼����Ҫ��֧�ֺ���                 
struct __FILE 
{ 
	int handle; 
}; 

FILE __stdout;       
//����_sys_exit()�Ա���ʹ�ð�����ģʽ    
void _sys_exit(int x) 
{ 
	x = x; 
} 
//�ض���fputc���� 
int fputc(int ch, FILE *f)
{ 	
	while((USART1->SR&0X40)==0);//ѭ������,ֱ���������   
	USART1->DR = (u8) ch;      
	return ch;
}
#endif
 
#if EN_USART1_RX   //���ʹ���˽���
//����1�жϷ������
//ע��,��ȡUSARTx->SR�ܱ���Ī������Ĵ���   	
u8 USART_RX_BUF[USART_REC_LEN];     //接收缓冲,最大USART_REC_LEN个字节.
//接收状态
//bit15：	接收完成标志
//bit14：	接收到0x0d
//bit13~0：	接收到的有效字节数目
u16 USART_RX_STA=0;       //接收状态标记

// 舵机协议相关变量
u8 SERVO_RX_BUF[SERVO_MAX_PACKET_LEN]; //舵机接收缓冲
u8 SERVO_RX_STA = 0;					//舵机接收状态: 0-等待帧头1, 1-等待帧头2, 2-接收数据
u8 SERVO_RX_CNT = 0;					//舵机接收计数器
u8 SERVO_PKT_LEN = 0;					//舵机数据包长度

// 串口屏通信相关变量
u8 SCREEN_RX_BUF[SCREEN_REC_LEN];		//串口屏接收缓冲
u8 SCREEN_RX_STA = 0;					//串口屏接收状态
u8 SCREEN_RX_CNT = 0;					//串口屏接收计数

// 舵机位置跟踪变量
u16 SERVO1_POSITION = 500;				//舵机1当前位置，初始值为中位
u16 SERVO2_POSITION = 500;				//舵机2当前位置，初始值为中位	

// 角度显示控制变量
u8 auto_refresh_enabled = 0;			//自动刷新禁用，只有按显示按钮时才更新

//��ʼ��IO ����1 
//bound:������
void uart_init(u32 bound){
   //GPIO�˿�����
  GPIO_InitTypeDef GPIO_InitStructure;
	USART_InitTypeDef USART_InitStructure;
	NVIC_InitTypeDef NVIC_InitStructure;
	
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA,ENABLE); //ʹ��GPIOAʱ��
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1,ENABLE);//ʹ��USART1ʱ��
 
	//����1��Ӧ���Ÿ���ӳ��
	GPIO_PinAFConfig(GPIOA,GPIO_PinSource9,GPIO_AF_USART1); //GPIOA9����ΪUSART1
	GPIO_PinAFConfig(GPIOA,GPIO_PinSource10,GPIO_AF_USART1); //GPIOA10����ΪUSART1
	
	//USART1�˿�����
  GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9 | GPIO_Pin_10; //GPIOA9��GPIOA10
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;//���ù���
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;	//�ٶ�50MHz
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP; //���츴�����
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP; //����
	GPIO_Init(GPIOA,&GPIO_InitStructure); //��ʼ��PA9��PA10

   //USART1 ��ʼ������
	USART_InitStructure.USART_BaudRate = bound;//����������
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;//�ֳ�Ϊ8λ���ݸ�ʽ
	USART_InitStructure.USART_StopBits = USART_StopBits_1;//һ��ֹͣλ
	USART_InitStructure.USART_Parity = USART_Parity_No;//����żУ��λ
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;//��Ӳ������������
	USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;	//�շ�ģʽ
  USART_Init(USART1, &USART_InitStructure); //��ʼ������1
	
  USART_Cmd(USART1, ENABLE);  //ʹ�ܴ���1 
	
	//USART_ClearFlag(USART1, USART_FLAG_TC);
	
#if EN_USART1_RX	
	USART_ITConfig(USART1, USART_IT_RXNE, ENABLE);//��������ж�

	//Usart1 NVIC ����
  NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;//����1�ж�ͨ��
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=3;//��ռ���ȼ�3
	NVIC_InitStructure.NVIC_IRQChannelSubPriority =3;		//�����ȼ�3
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;			//IRQͨ��ʹ��
	NVIC_Init(&NVIC_InitStructure);	//����ָ���Ĳ�����ʼ��VIC�Ĵ�����

#endif
	
}


void USART1_IRQHandler(void)                	//串口1中断服务程序
{
	u8 Res;
#if SYSTEM_SUPPORT_OS 		//如果SYSTEM_SUPPORT_OS为真，则需要支持OS.
	OSIntEnter();    
#endif
	if(USART_GetITStatus(USART1, USART_IT_RXNE) != RESET)  //接收中断
	{
		Res = USART_ReceiveData(USART1);	//读取接收到的数据
		
		// 舵机协议包状态机处理
		switch(SERVO_RX_STA)
		{
			case 0:	// 等待第一个帧头 0x55
				if(Res == SERVO_FRAME_HEADER)
				{
					SERVO_RX_STA = 1;
					SERVO_RX_CNT = 0;
				}
				break;
				
			case 1:	// 等待第二个帧头 0x55
				if(Res == SERVO_FRAME_HEADER)
				{
					SERVO_RX_STA = 2;
					SERVO_RX_BUF[0] = SERVO_FRAME_HEADER;
					SERVO_RX_BUF[1] = SERVO_FRAME_HEADER;
					SERVO_RX_CNT = 2;
				}
				else
				{
					SERVO_RX_STA = 0; // 重新开始
				}
				break;
				
			case 2:	// 接收数据包
				SERVO_RX_BUF[SERVO_RX_CNT] = Res;
				SERVO_RX_CNT++;
				
				if(SERVO_RX_CNT == 5) // 收到ID、Length、Cmd
				{
					SERVO_PKT_LEN = SERVO_RX_BUF[3] + 3; // Length + 帧头(2) + 校验(1)
					if(SERVO_PKT_LEN > SERVO_MAX_PACKET_LEN)
					{
						SERVO_RX_STA = 0; // 包长度错误，重新开始
						break;
					}
				}
				
				if(SERVO_RX_CNT >= 5 && SERVO_RX_CNT >= SERVO_PKT_LEN)
				{
					// 数据包接收完成，处理数据包
					Servo_ProcessPacket();
					// 重新开始等待下一包
					SERVO_RX_STA = 0;
				}
				
				if(SERVO_RX_CNT >= SERVO_MAX_PACKET_LEN)
				{
					SERVO_RX_STA = 0; // 防止溢出
				}
				break;
		}
  } 
#if SYSTEM_SUPPORT_OS 	//如果SYSTEM_SUPPORT_OS为真，则需要支持OS.
	OSIntExit();  											 
#endif
} 
#endif	

// 舵机控制函数实现

// 串口发送数组
void USART_SendArray(u8 *arr, u16 len) 
{
	u16 i;
	for (i = 0; i < len; i++) 
	{
		while(USART_GetFlagStatus(USART1, USART_FLAG_TXE) != SET);
		USART_SendData(USART1, arr[i]);
	}
	while(USART_GetFlagStatus(USART1, USART_FLAG_TC) != SET); // 等待发送完成
}

// 计算舵机协议校验和
u8 Servo_CalculateChecksum(u8 *data, u8 len) 
{
	u8 sum = 0;
	u8 i;
	for (i = 0; i < len; i++) 
	{
		sum += data[i];
	}
	return ~sum; // 取反
}

// 设置舵机为电机模式并控制转动速度
void Servo_SetMotorMode(u8 id, s16 speed) 
{
	u8 cmd_packet[10];
	
	// 构建命令包: 55 55 ID 07 1D 01 00 speed_low speed_high checksum
	cmd_packet[0] = 0x55;								// 帧头1
	cmd_packet[1] = 0x55;								// 帧头2
	cmd_packet[2] = id;									// 舵机ID
	cmd_packet[3] = 0x07;								// 数据长度
	cmd_packet[4] = 0x1D;								// 指令(SERVO_OR_MOTOR_MODE_WRITE = 29 = 0x1D)
	cmd_packet[5] = 0x01;								// 参数1: 电机模式
	cmd_packet[6] = 0x00;								// 参数2: 空值
	cmd_packet[7] = (u8)(speed & 0xFF);					// 参数3: 速度低字节
	cmd_packet[8] = (u8)((speed >> 8) & 0xFF);			// 参数4: 速度高字节
	
	// 计算校验和 (从ID开始到速度高字节)
	cmd_packet[9] = Servo_CalculateChecksum(&cmd_packet[2], 7);
	
	// 发送命令包
	USART_SendArray(cmd_packet, 10);
}

// 装载舵机电机(上电状态)
void Servo_LoadMotor(u8 id) 
{
	u8 cmd_packet[7];
	
	// 构建命令包: 55 55 ID 04 1F 01 checksum
	cmd_packet[0] = 0x55;								// 帧头1
	cmd_packet[1] = 0x55;								// 帧头2
	cmd_packet[2] = id;									// 舵机ID
	cmd_packet[3] = 0x04;								// 数据长度
	cmd_packet[4] = 0x1F;								// 指令(SERVO_LOAD_OR_UNLOAD_WRITE = 31 = 0x1F)
	cmd_packet[5] = 0x01;								// 参数1: 1=装载电机，0=卸载电机
	
	// 计算校验和 (从ID开始到参数)
	cmd_packet[6] = Servo_CalculateChecksum(&cmd_packet[2], 4);
	
	// 发送命令包
	USART_SendArray(cmd_packet, 7);
}

// 舵机位置控制(位置模式，带时间)
void Servo_MoveToPosition(u8 id, u16 position, u16 time) 
{
	u8 cmd_packet[10];
	
	// 构建命令包: 55 55 ID 07 01 pos_low pos_high time_low time_high checksum
	cmd_packet[0] = 0x55;								// 帧头1
	cmd_packet[1] = 0x55;								// 帧头2
	cmd_packet[2] = id;									// 舵机ID
	cmd_packet[3] = 0x07;								// 数据长度
	cmd_packet[4] = 0x01;								// 指令(SERVO_MOVE_TIME_WRITE = 1)
	cmd_packet[5] = (u8)(position & 0xFF);				// 参数1: 角度低字节
	cmd_packet[6] = (u8)((position >> 8) & 0xFF);		// 参数2: 角度高字节  
	cmd_packet[7] = (u8)(time & 0xFF);					// 参数3: 时间低字节
	cmd_packet[8] = (u8)((time >> 8) & 0xFF);			// 参数4: 时间高字节
	
	// 计算校验和 (从ID开始到时间高字节)
	cmd_packet[9] = Servo_CalculateChecksum(&cmd_packet[2], 7);
	
	// 发送命令包
	USART_SendArray(cmd_packet, 10);
}

// 旧的读取舵机位置函数已删除，使用新的u16 Servo_ReadPosition函数

// 处理接收到的舵机数据包
u8 Servo_ProcessPacket(void) 
{
	u8 checksum;
	u16 position;
	u8 servo_id;
	
	// 检查数据包长度和校验和
	if(SERVO_RX_CNT < 6) return 0; // 最小包长度检查
	
	// 提取舵机ID
	servo_id = SERVO_RX_BUF[2];
	
	// 计算校验和
	checksum = Servo_CalculateChecksum(&SERVO_RX_BUF[2], SERVO_RX_CNT - 3);
	if(checksum != SERVO_RX_BUF[SERVO_RX_CNT - 1]) {
		return 0; // 校验失败
	}
	
	// 判断是否为位置读取响应 (指令0x1C - 读取位置命令的响应)
	if(SERVO_RX_BUF[4] == 0x1C && SERVO_RX_CNT >= 8) {
		// 提取位置数据 (低字节在前，高字节在后)
		position = SERVO_RX_BUF[5] | (SERVO_RX_BUF[6] << 8);

		// 更新对应舵机的位置变量
		if(servo_id == 1) {
			SERVO1_POSITION = position;
		} else if(servo_id == 2) {
			SERVO2_POSITION = position;
		}

		// 更新角度反馈测试的全局变量
		servo_feedback_position = position;
		servo_feedback_received = 1;

		return 1; // 成功处理
	}
	
	return 0;
}

// UART2初始化函数(串口屏通信)
void uart2_init(u32 bound)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	USART_InitTypeDef USART_InitStructure;
	NVIC_InitTypeDef NVIC_InitStructure;
	
	// 使能UART2和GPIOA时钟
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2, ENABLE);
 
	// PA2复用为USART2_TX, PA3复用为USART2_RX
	GPIO_PinAFConfig(GPIOA, GPIO_PinSource2, GPIO_AF_USART2);
	GPIO_PinAFConfig(GPIOA, GPIO_PinSource3, GPIO_AF_USART2);
	
	// UART2端口配置
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2 | GPIO_Pin_3;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
	GPIO_Init(GPIOA, &GPIO_InitStructure);

	// UART2初始化设置
	USART_InitStructure.USART_BaudRate = bound;
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;
	USART_InitStructure.USART_StopBits = USART_StopBits_1;
	USART_InitStructure.USART_Parity = USART_Parity_No;
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
	USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
  	USART_Init(USART2, &USART_InitStructure);
  	
#if EN_USART2_RX
	// Usart2 NVIC配置
	NVIC_InitStructure.NVIC_IRQChannel = USART2_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 3;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 3;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Init(&NVIC_InitStructure);
	
	USART_ITConfig(USART2, USART_IT_RXNE, ENABLE);
#endif
	
	USART_Cmd(USART2, ENABLE);
}

// UART2中断处理函数
void USART2_IRQHandler(void)
{
	u8 Res;
#if SYSTEM_SUPPORT_OS
	OSIntEnter();    
#endif
	if(USART_GetITStatus(USART2, USART_IT_RXNE) != RESET)
	{
		Res = USART_ReceiveData(USART2);
		
		// 串口屏数据接收处理
		if(SCREEN_RX_CNT < (SCREEN_REC_LEN-1))
		{
			SCREEN_RX_BUF[SCREEN_RX_CNT] = Res;
			SCREEN_RX_CNT++;
			
			// 检查是否为串口屏数据包结束标志 0xFF 0xFF 0xFF
			if(SCREEN_RX_CNT >= 3)
			{
				if(SCREEN_RX_BUF[SCREEN_RX_CNT-3] == 0xFF && 
				   SCREEN_RX_BUF[SCREEN_RX_CNT-2] == 0xFF && 
				   SCREEN_RX_BUF[SCREEN_RX_CNT-1] == 0xFF)
				{
					SCREEN_RX_STA = 1;  // 标记接收完成
				}
			}
		}
		else
		{
			SCREEN_RX_CNT = 0;  // 缓冲区溢出，重置
		}
	}
#if SYSTEM_SUPPORT_OS
	OSIntExit();  
#endif
}

// UART2发送数组
void USART2_SendArray(u8 *arr, u16 len)
{
	u16 i;
	for (i = 0; i < len; i++)
	{
		while(USART_GetFlagStatus(USART2, USART_FLAG_TXE) != SET);
		USART_SendData(USART2, arr[i]);
	}
	while(USART_GetFlagStatus(USART2, USART_FLAG_TC) != SET);
}

// 串口屏命令处理 - 平面坐标控制模式  
void Screen_ProcessCommand(void)
{
	u8 component_id;
	static float Y_coord = 0.0f, Z_coord = 0.0f;  // 保存输入的坐标值
	static u8 coord_read_state = 0;  // 0=等待按键, 1=读取Y坐标, 2=读取Z坐标, 3=执行控制
	static u8 square_point_index = 0;  // 正方形轨迹点索引 (0-4)
	
	if(SCREEN_RX_STA == 1 && SCREEN_RX_CNT >= 7)  // 需要7字节数据
	{
		// 检查是否为控件事件 0x65 00 xx yy FF FF FF
		if(SCREEN_RX_BUF[0] == 0x65 && 
		   SCREEN_RX_BUF[1] == 0x00 &&
		   SCREEN_RX_BUF[4] == 0xFF && 
		   SCREEN_RX_BUF[5] == 0xFF && 
		   SCREEN_RX_BUF[6] == 0xFF)
		{
			component_id = SCREEN_RX_BUF[2];  // 控件ID在第3字节
			// 事件类型在第4字节，但我们暂时不关心
			
			// 根据控件ID处理事件
			switch(component_id)
			{
				case 1:  // b2 移动激光点按钮
					// 按键确认音
					BEEP_Sound(100);

					// 显示调试信息：开始读取坐标
					{
						u8 debug_str[25];
						u8 len = 0;
						debug_str[len++] = 't'; debug_str[len++] = '0'; debug_str[len++] = '.';
						debug_str[len++] = 't'; debug_str[len++] = 'x'; debug_str[len++] = 't';
						debug_str[len++] = '='; debug_str[len++] = '"';
						debug_str[len++] = 'R'; debug_str[len++] = 'e'; debug_str[len++] = 'a'; debug_str[len++] = 'd'; debug_str[len++] = 'i'; debug_str[len++] = 'n'; debug_str[len++] = 'g'; debug_str[len++] = '.'; debug_str[len++] = '.'; debug_str[len++] = '.';
						debug_str[len++] = '"';
						debug_str[len++] = 0xFF; debug_str[len++] = 0xFF; debug_str[len++] = 0xFF;
						USART2_SendArray(debug_str, len);
					}
					delay_ms(500);

					// 开始读取坐标过程
					coord_read_state = 1;
					Screen_RequestCoordinateInput();
					break;
					
				case 2:  // b3 正方形轨迹循环按钮
					{
						// 定义正方形轨迹的5个点：(50,50) → (50,-50) → (-50,-50) → (-50,50) → (50,50)
						float square_points[5][2] = {
							{50.0f, 50.0f},    // 0: 右上角
							{50.0f, -50.0f},   // 1: 右下角
							{-50.0f, -50.0f},  // 2: 左下角
							{-50.0f, 50.0f},   // 3: 左上角
							{50.0f, 50.0f}     // 4: 回到右上角
						};
						float target_Y = square_points[square_point_index][0];
						float target_Z = square_points[square_point_index][1];

						// 按键确认音
						BEEP_Sound(100);

						// 显示当前移动到的点和位置描述
						{
							u8 status_str[30];
							u8 len = 0;
							const char* position_names[5] = {"RT", "RB", "LB", "LT", "RT"};  // 右上、右下、左下、左上、右上

							status_str[len++] = 't'; status_str[len++] = '0'; status_str[len++] = '.';
							status_str[len++] = 't'; status_str[len++] = 'x'; status_str[len++] = 't';
							status_str[len++] = '='; status_str[len++] = '"';
							status_str[len++] = 'S'; status_str[len++] = 'q'; status_str[len++] = 'u'; status_str[len++] = 'a'; status_str[len++] = 'r'; status_str[len++] = 'e';
							status_str[len++] = ' '; status_str[len++] = '0' + (square_point_index + 1);
							status_str[len++] = '/'; status_str[len++] = '5';
							status_str[len++] = ' ';

							// 添加位置描述
							status_str[len++] = position_names[square_point_index][0];
							status_str[len++] = position_names[square_point_index][1];

							status_str[len++] = '"';
							status_str[len++] = 0xFF; status_str[len++] = 0xFF; status_str[len++] = 0xFF;
							USART2_SendArray(status_str, len);
						}
						delay_ms(500);

						// 移动到目标点
						Servo_MoveToCoordinate(target_Y, target_Z);

						// 检查是否完成一个完整循环
						if(square_point_index == 4) {
							// 循环完成提示音 - 两声短音
							BEEP_Sound(150);
							delay_ms(100);
							BEEP_Sound(150);

							// 显示循环完成提示
							delay_ms(200);
							{
								u8 cycle_str[25];
								u8 len = 0;
								cycle_str[len++] = 't'; cycle_str[len++] = '0'; cycle_str[len++] = '.';
								cycle_str[len++] = 't'; cycle_str[len++] = 'x'; cycle_str[len++] = 't';
								cycle_str[len++] = '='; cycle_str[len++] = '"';
								cycle_str[len++] = 'C'; cycle_str[len++] = 'y'; cycle_str[len++] = 'c'; cycle_str[len++] = 'l'; cycle_str[len++] = 'e';
								cycle_str[len++] = ' '; cycle_str[len++] = 'D'; cycle_str[len++] = 'o'; cycle_str[len++] = 'n'; cycle_str[len++] = 'e';
								cycle_str[len++] = '!';
								cycle_str[len++] = '"';
								cycle_str[len++] = 0xFF; cycle_str[len++] = 0xFF; cycle_str[len++] = 0xFF;
								USART2_SendArray(cycle_str, len);
							}
							delay_ms(800);
						}

						// 更新索引，循环到下一个点
						square_point_index = (square_point_index + 1) % 5;
					}
					break;
					
				case 3:  // b4 卸载按钮 - 卸载舵机电机
					// 卸载确认音 - 长音表示卸载
					BEEP_Sound(300);

					// 显示按键ID作为调试
					{
						u8 debug_str[20];
						u8 len = 0;
						debug_str[len++] = 't'; debug_str[len++] = '0'; debug_str[len++] = '.';
						debug_str[len++] = 't'; debug_str[len++] = 'x'; debug_str[len++] = 't';
						debug_str[len++] = '='; debug_str[len++] = '"';
						debug_str[len++] = 'U'; debug_str[len++] = 'n'; debug_str[len++] = 'l'; debug_str[len++] = 'o'; debug_str[len++] = 'a'; debug_str[len++] = 'd'; debug_str[len++] = '.'; debug_str[len++] = '.'; debug_str[len++] = '.';
						debug_str[len++] = '"';
						debug_str[len++] = 0xFF; debug_str[len++] = 0xFF; debug_str[len++] = 0xFF;
						USART2_SendArray(debug_str, len);
					}
					delay_ms(500);  // 显示延迟
					// 发送卸载命令 (参数为0)
					{
						u8 cmd_packet[7];
						// 卸载舵机1
						cmd_packet[0] = 0x55; cmd_packet[1] = 0x55;
						cmd_packet[2] = 1; cmd_packet[3] = 0x04; cmd_packet[4] = 0x1F; cmd_packet[5] = 0x00;
						cmd_packet[6] = Servo_CalculateChecksum(&cmd_packet[2], 4);
						USART_SendArray(cmd_packet, 7);
						delay_ms(50);
						
						// 卸载舵机2
						cmd_packet[2] = 2;
						cmd_packet[6] = Servo_CalculateChecksum(&cmd_packet[2], 4);
						USART_SendArray(cmd_packet, 7);
					}
					break;

				case 8:  // b5 角度反馈测试按钮 (按键ID为8)
					// 测试确认音
					BEEP_Sound(150);

					// 启动角度反馈测试
					Servo_AngleFeedbackTest();
					break;

				default:
					// 显示未知按键ID
					{
						u8 debug_str[20];
						u8 len = 0;
						debug_str[len++] = 't'; debug_str[len++] = '4'; debug_str[len++] = '.';
						debug_str[len++] = 't'; debug_str[len++] = 'x'; debug_str[len++] = 't';
						debug_str[len++] = '='; debug_str[len++] = '"';
						debug_str[len++] = 'I'; debug_str[len++] = 'D'; debug_str[len++] = '?';
						debug_str[len++] = '"';
						debug_str[len++] = 0xFF; debug_str[len++] = 0xFF; debug_str[len++] = 0xFF;
						USART2_SendArray(debug_str, len);
					}
					break;
			}
			
			// 清除接收状态（只对控件事件清除）
			SCREEN_RX_STA = 0;
			SCREEN_RX_CNT = 0;
		}
		// 检查是否为数值数据返回 0x71
		else if(SCREEN_RX_BUF[0] == 0x71 && SCREEN_RX_CNT >= 8)
		{
			// 解析4字节数值（小端模式）
			u32 value = (u32)SCREEN_RX_BUF[1] | 
			           ((u32)SCREEN_RX_BUF[2] << 8) | 
			           ((u32)SCREEN_RX_BUF[3] << 16) | 
			           ((u32)SCREEN_RX_BUF[4] << 24);
			
			// 根据当前状态处理坐标读取
			if(coord_read_state == 1) {
				// 读取到Y坐标
				u8 cmd_str[20];
				u8 len = 0;

				Y_coord = (float)(s32)value;  // 转换为有符号整数再转浮点
				coord_read_state = 2;

				// 显示读取到的Y坐标值
				Screen_UpdateCoordinateDisplay(Y_coord, 0.0f, 0.0f, 0.0f);

				// 显示状态：正在读取Z坐标
				len = 0;
				cmd_str[len++] = 't'; cmd_str[len++] = '0'; cmd_str[len++] = '.';
				cmd_str[len++] = 't'; cmd_str[len++] = 'x'; cmd_str[len++] = 't';
				cmd_str[len++] = '='; cmd_str[len++] = '"';
				cmd_str[len++] = 'R'; cmd_str[len++] = 'e'; cmd_str[len++] = 'a'; cmd_str[len++] = 'd'; cmd_str[len++] = ' '; cmd_str[len++] = 'Z'; cmd_str[len++] = '.'; cmd_str[len++] = '.'; cmd_str[len++] = '.';
				cmd_str[len++] = '"';
				cmd_str[len++] = 0xFF; cmd_str[len++] = 0xFF; cmd_str[len++] = 0xFF;
				USART2_SendArray(cmd_str, len);

				delay_ms(200);  // 增加延迟确保显示
				// 继续读取Z坐标 - 发送get n1.val命令
				len = 0;
				cmd_str[len++] = 'g'; cmd_str[len++] = 'e'; cmd_str[len++] = 't';
				cmd_str[len++] = ' '; cmd_str[len++] = 'n'; cmd_str[len++] = '1';
				cmd_str[len++] = '.'; cmd_str[len++] = 'v'; cmd_str[len++] = 'a'; cmd_str[len++] = 'l';
				cmd_str[len++] = 0xFF; cmd_str[len++] = 0xFF; cmd_str[len++] = 0xFF;
				USART2_SendArray(cmd_str, len);
			}
			else if(coord_read_state == 2) {
				// 读取到Z坐标
				Z_coord = (float)(s32)value;
				coord_read_state = 0;  // 重置状态

				// 显示完整的坐标信息
				Screen_UpdateCoordinateDisplay(Y_coord, Z_coord, 0.0f, 0.0f);
				delay_ms(300);  // 显示延迟

				// 显示状态：正在移动
				{
					u8 status_str[20];
					u8 len = 0;
					status_str[len++] = 't'; status_str[len++] = '0'; status_str[len++] = '.';
					status_str[len++] = 't'; status_str[len++] = 'x'; status_str[len++] = 't';
					status_str[len++] = '='; status_str[len++] = '"';
					status_str[len++] = 'M'; status_str[len++] = 'o'; status_str[len++] = 'v'; status_str[len++] = 'i'; status_str[len++] = 'n'; status_str[len++] = 'g'; status_str[len++] = '.'; status_str[len++] = '.'; status_str[len++] = '.';
					status_str[len++] = '"';
					status_str[len++] = 0xFF; status_str[len++] = 0xFF; status_str[len++] = 0xFF;
					USART2_SendArray(status_str, len);
				}
				delay_ms(200);

				// 执行平面坐标逆解并控制舵机
				Servo_MoveToCoordinate(Y_coord, Z_coord);
			}
			
			// 清除接收状态（对数值返回清除）
			SCREEN_RX_STA = 0;
			SCREEN_RX_CNT = 0;
		}
		else
		{
			// 未识别的数据，清除接收状态
			SCREEN_RX_STA = 0;
			SCREEN_RX_CNT = 0;
		}
	}
}

// 更新串口屏角度显示 - 显示0-240度角度值
void Screen_UpdateAngleDisplay(void)
{
	u8 cmd_str[20];
	u8 len;
	u16 angle1, angle2;
	
	// 将位置值转换为角度 (0-1000 对应 0-240度)
	angle1 = (u16)((u32)SERVO1_POSITION * 240 / 1000);
	angle2 = (u16)((u32)SERVO2_POSITION * 240 / 1000);
	
	// 发送舵机1角度到文本框t0 (ID=4)
	len = 0;
	cmd_str[len++] = 't';
	cmd_str[len++] = '4';
	cmd_str[len++] = '.';
	cmd_str[len++] = 't';
	cmd_str[len++] = 'x';
	cmd_str[len++] = 't';
	cmd_str[len++] = '=';
	cmd_str[len++] = '"';
	
	// 角度数字转字符串
	if(angle1 >= 100) {
		cmd_str[len++] = '0' + (angle1 / 100);
	}
	if(angle1 >= 10) {
		cmd_str[len++] = '0' + ((angle1 % 100) / 10);
	}
	cmd_str[len++] = '0' + (angle1 % 10);
	
	cmd_str[len++] = '"';
	cmd_str[len++] = 0xFF;
	cmd_str[len++] = 0xFF;
	cmd_str[len++] = 0xFF;
	
	USART2_SendArray(cmd_str, len);
	delay_ms(50);  // 减少延迟提高响应速度
	
	// 发送舵机2角度到文本框t1 (ID=5)
	len = 0;
	cmd_str[len++] = 't';
	cmd_str[len++] = '5';
	cmd_str[len++] = '.';
	cmd_str[len++] = 't';
	cmd_str[len++] = 'x';
	cmd_str[len++] = 't';
	cmd_str[len++] = '=';
	cmd_str[len++] = '"';
	
	if(angle2 >= 100) {
		cmd_str[len++] = '0' + (angle2 / 100);
	}
	if(angle2 >= 10) {
		cmd_str[len++] = '0' + ((angle2 % 100) / 10);
	}
	cmd_str[len++] = '0' + (angle2 % 10);
	
	cmd_str[len++] = '"';
	cmd_str[len++] = 0xFF;
	cmd_str[len++] = 0xFF;
	cmd_str[len++] = 0xFF;
	
	USART2_SendArray(cmd_str, len);
}

// 平面坐标逆解算法实现

// 高精度平面坐标逆解函数（带多级补偿）
void Plane_CoordinateInverse(float Y, float Z, float *theta, float *phi)
{
	float corrected_Y, corrected_Z;
	float distance_to_target;
	
	// 第一步：激光器偏移补偿
	corrected_Y = Y - LASER_OFFSET_Y;
	corrected_Z = Z - LASER_OFFSET_Z;

#if ENABLE_ADVANCED_CALIBRATION
	// 第二步：应用校准补偿
	Coordinate_ApplyCompensation(&corrected_Y, &corrected_Z);
#endif

	// 第三步：计算到目标点的实际距离（考虑Z轴偏移的空间距离）
	distance_to_target = sqrtf(DISTANCE_TO_PLANE * DISTANCE_TO_PLANE + corrected_Z * corrected_Z);
	
	// 第四步：改进的几何逆解算法
	// 水平角计算（考虑非线性补偿）
	*theta = atan2f(-corrected_Y * Y_AXIS_NONLINEAR_FACTOR, DISTANCE_Y_AXIS);
	
	// 俯仰角计算（使用更精确的几何模型）
	float vertical_angle = atan2f(corrected_Z, DISTANCE_TO_PLANE);
	*phi = vertical_angle * Z_AXIS_NONLINEAR_FACTOR;
	
	// 第五步：角度限制和精度优化
	// 限制角度范围，避免超出舵机工作区间
	if(*theta > PI/3) *theta = PI/3;   // ±60度限制
	if(*theta < -PI/3) *theta = -PI/3;
	if(*phi > PI/6) *phi = PI/6;       // ±30度限制  
	if(*phi < -PI/6) *phi = -PI/6;
}

// 角度转换为舵机位置值
u16 AngleToServoPosition(float angle_rad)
{
	float angle_deg = angle_rad * 180.0f / PI;
	// 舵机0-240°对应位置0-1000，中心120°对应500
	// 转换公式：position = (angle_deg + 120) * 1000 / 240
	u16 position = (u16)((angle_deg + 120.0f) * 1000.0f / 240.0f);
	
	// 限制范围0-1000
	if(position > 1000) position = 1000;
	return position;
}

// 根据平面坐标控制舵机移动
// 坐标系定义：以A4纸中心为原点(0,0)
// Y轴：水平方向，正值向右，负值向左 (-105 ~ +105mm)
// Z轴：垂直方向，正值向上，负值向下 (-148.5 ~ +148.5mm)
void Servo_MoveToCoordinate(float Y, float Z)
{
	float theta, phi;
	u16 servo1_pos, servo2_pos;

	// 限制坐标范围
	if(Y > 105.0f) Y = 105.0f;
	if(Y < -105.0f) Y = -105.0f;
	if(Z > 148.5f) Z = 148.5f;
	if(Z < -148.5f) Z = -148.5f;
	
	// 计算逆解
	Plane_CoordinateInverse(Y, Z, &theta, &phi);
	
	// 转换为舵机位置
	servo1_pos = AngleToServoPosition(theta);  // 舵机1水平轴
	servo2_pos = AngleToServoPosition(phi);    // 舵机2垂直轴
	
	// 控制舵机移动
	Servo_MoveToPosition(1, servo1_pos, 0);  // 0ms运动时间 - 最快速度
	delay_ms(50);
	Servo_MoveToPosition(2, servo2_pos, 0);  // 0ms运动时间 - 最快速度

	// 等待舵机移动完成
	delay_ms(300);  // 减少等待时间，因为舵机运动更快

	// 更新位置跟踪
	SERVO1_POSITION = servo1_pos;
	SERVO2_POSITION = servo2_pos;

	// 更新角度显示
	Screen_UpdateCoordinateDisplay(Y, Z, theta * 180.0f / PI, phi * 180.0f / PI);

	// 移动完成提示音
	BEEP_Sound(200);

	// 显示移动完成状态
	{
		u8 complete_str[20];
		u8 len = 0;
		complete_str[len++] = 't'; complete_str[len++] = '0'; complete_str[len++] = '.';
		complete_str[len++] = 't'; complete_str[len++] = 'x'; complete_str[len++] = 't';
		complete_str[len++] = '='; complete_str[len++] = '"';
		complete_str[len++] = 'C'; complete_str[len++] = 'o'; complete_str[len++] = 'm'; complete_str[len++] = 'p'; complete_str[len++] = 'l'; complete_str[len++] = 'e'; complete_str[len++] = 't'; complete_str[len++] = 'e'; complete_str[len++] = '!';
		complete_str[len++] = '"';
		complete_str[len++] = 0xFF; complete_str[len++] = 0xFF; complete_str[len++] = 0xFF;
		USART2_SendArray(complete_str, len);
	}
}

// ==================== 高精度校准补偿系统 ====================

// 校准数据存储
static CalibrationPoint_t calibration_points[CALIBRATION_POINTS_COUNT];
static u8 calibration_count = 0;
static u8 calibration_enabled = 0;

// 系统误差补偿参数（通过校准计算得出）
static float Y_offset_compensation = 0.0f;      // Y轴系统偏移
static float Z_offset_compensation = 0.0f;      // Z轴系统偏移
static float Y_scale_compensation = 1.0f;       // Y轴比例补偿
static float Z_scale_compensation = 1.0f;       // Z轴比例补偿
static float cross_coupling_YZ = 0.0f;          // Y-Z轴耦合补偿
static float cross_coupling_ZY = 0.0f;          // Z-Y轴耦合补偿

// 启动校准过程
void Calibration_Start(void)
{
	calibration_count = 0;
	calibration_enabled = 1;
	
	// 重置补偿参数
	Y_offset_compensation = 0.0f;
	Z_offset_compensation = 0.0f;
	Y_scale_compensation = 1.0f;
	Z_scale_compensation = 1.0f;
	cross_coupling_YZ = 0.0f;
	cross_coupling_ZY = 0.0f;
	
	// 显示校准开始信息
	{
		u8 msg[25];
		u8 len = 0;
		msg[len++] = 't'; msg[len++] = '0'; msg[len++] = '.';
		msg[len++] = 't'; msg[len++] = 'x'; msg[len++] = 't';
		msg[len++] = '='; msg[len++] = '"';
		msg[len++] = 'C'; msg[len++] = 'a'; msg[len++] = 'l'; msg[len++] = 'i'; msg[len++] = 'b'; msg[len++] = ' '; msg[len++] = 'S'; msg[len++] = 't'; msg[len++] = 'a'; msg[len++] = 'r'; msg[len++] = 't';
		msg[len++] = '"';
		msg[len++] = 0xFF; msg[len++] = 0xFF; msg[len++] = 0xFF;
		USART2_SendArray(msg, len);
	}
}

// 添加校准点
void Calibration_AddPoint(float target_Y, float target_Z, float actual_Y, float actual_Z)
{
	if(calibration_count < CALIBRATION_POINTS_COUNT) {
		calibration_points[calibration_count].target_Y = target_Y;
		calibration_points[calibration_count].target_Z = target_Z;
		calibration_points[calibration_count].actual_Y = actual_Y;
		calibration_points[calibration_count].actual_Z = actual_Z;
		calibration_points[calibration_count].error_Y = actual_Y - target_Y;
		calibration_points[calibration_count].error_Z = actual_Z - target_Z;
		calibration_count++;
	}
}

// 计算补偿参数（最小二乘法）
void Calibration_Calculate(void)
{
	if(calibration_count < 4) return;  // 至少需要4个点
	
	float sum_target_Y = 0, sum_target_Z = 0;
	float sum_error_Y = 0, sum_error_Z = 0;
	float sum_target_Y2 = 0, sum_target_Z2 = 0;
	float sum_target_YZ = 0;
	float sum_error_Y_target_Y = 0, sum_error_Z_target_Z = 0;
	float sum_error_Y_target_Z = 0, sum_error_Z_target_Y = 0;
	u8 i;
	
	// 计算统计量
	for(i = 0; i < calibration_count; i++) {
		float ty = calibration_points[i].target_Y;
		float tz = calibration_points[i].target_Z;
		float ey = calibration_points[i].error_Y;
		float ez = calibration_points[i].error_Z;
		
		sum_target_Y += ty;
		sum_target_Z += tz;
		sum_error_Y += ey;
		sum_error_Z += ez;
		sum_target_Y2 += ty * ty;
		sum_target_Z2 += tz * tz;
		sum_target_YZ += ty * tz;
		sum_error_Y_target_Y += ey * ty;
		sum_error_Z_target_Z += ez * tz;
		sum_error_Y_target_Z += ey * tz;
		sum_error_Z_target_Y += ez * ty;
	}
	
	float n = (float)calibration_count;
	
	// 计算偏移补偿
	Y_offset_compensation = sum_error_Y / n;
	Z_offset_compensation = sum_error_Z / n;
	
	// 计算比例补偿 (简化的线性回归)
	float denom_Y = sum_target_Y2 - (sum_target_Y * sum_target_Y) / n;
	float denom_Z = sum_target_Z2 - (sum_target_Z * sum_target_Z) / n;
	
	if(denom_Y > 0.001f) {
		Y_scale_compensation = 1.0f + (sum_error_Y_target_Y - sum_error_Y * sum_target_Y / n) / denom_Y;
	}
	
	if(denom_Z > 0.001f) {
		Z_scale_compensation = 1.0f + (sum_error_Z_target_Z - sum_error_Z * sum_target_Z / n) / denom_Z;
	}
	
	// 计算耦合补偿（简化处理）
	if(denom_Z > 0.001f) {
		cross_coupling_YZ = (sum_error_Y_target_Z - sum_error_Y * sum_target_Z / n) / denom_Z;
	}
	
	if(denom_Y > 0.001f) {
		cross_coupling_ZY = (sum_error_Z_target_Y - sum_error_Z * sum_target_Y / n) / denom_Y;
	}
	
	calibration_enabled = 1;
	
	// 显示校准完成信息
	{
		u8 msg[25];
		u8 len = 0;
		msg[len++] = 't'; msg[len++] = '0'; msg[len++] = '.';
		msg[len++] = 't'; msg[len++] = 'x'; msg[len++] = 't';
		msg[len++] = '='; msg[len++] = '"';
		msg[len++] = 'C'; msg[len++] = 'a'; msg[len++] = 'l'; msg[len++] = 'i'; msg[len++] = 'b'; msg[len++] = ' '; msg[len++] = 'D'; msg[len++] = 'o'; msg[len++] = 'n'; msg[len++] = 'e';
		msg[len++] = '"';
		msg[len++] = 0xFF; msg[len++] = 0xFF; msg[len++] = 0xFF;
		USART2_SendArray(msg, len);
	}
}

// 应用补偿到坐标
void Coordinate_ApplyCompensation(float *Y, float *Z)
{
	if(!calibration_enabled) return;
	
	float original_Y = *Y;
	float original_Z = *Z;
	
	// 应用多级补偿
	*Y = (original_Y - Y_offset_compensation) * Y_scale_compensation - cross_coupling_YZ * original_Z;
	*Z = (original_Z - Z_offset_compensation) * Z_scale_compensation - cross_coupling_ZY * original_Y;
}

// 请求串口屏坐标输入
void Screen_RequestCoordinateInput(void)
{
	u8 cmd_str[20];
	u8 len;
	
	// 显示调试信息：正在读取Y坐标 (使用t0控件显示状态)
	len = 0;
	cmd_str[len++] = 't'; cmd_str[len++] = '0'; cmd_str[len++] = '.';
	cmd_str[len++] = 't'; cmd_str[len++] = 'x'; cmd_str[len++] = 't';
	cmd_str[len++] = '='; cmd_str[len++] = '"';
	cmd_str[len++] = 'R'; cmd_str[len++] = 'e'; cmd_str[len++] = 'a'; cmd_str[len++] = 'd'; cmd_str[len++] = ' '; cmd_str[len++] = 'Y'; cmd_str[len++] = '.'; cmd_str[len++] = '.'; cmd_str[len++] = '.';
	cmd_str[len++] = '"';
	cmd_str[len++] = 0xFF; cmd_str[len++] = 0xFF; cmd_str[len++] = 0xFF;
	USART2_SendArray(cmd_str, len);
	delay_ms(200);
	
	// 读取Y坐标 (n0控件，objname=n0)
	len = 0;
	cmd_str[len++] = 'g';
	cmd_str[len++] = 'e';
	cmd_str[len++] = 't';
	cmd_str[len++] = ' ';
	cmd_str[len++] = 'n';
	cmd_str[len++] = '0';
	cmd_str[len++] = '.';
	cmd_str[len++] = 'v';
	cmd_str[len++] = 'a';
	cmd_str[len++] = 'l';
	cmd_str[len++] = 0xFF;
	cmd_str[len++] = 0xFF;
	cmd_str[len++] = 0xFF;
	
	USART2_SendArray(cmd_str, len);
}

// 更新坐标和角度显示
void Screen_UpdateCoordinateDisplay(float Y, float Z, float theta_deg, float phi_deg)
{
	u8 cmd_str[30];
	u8 len;
	s16 coord_int, angle_int;
	
	// 显示坐标信息到文本框t0 - 格式: "Y:xx Z:xx" (Y:右+ Z:上+)
	len = 0;
	cmd_str[len++] = 't';
	cmd_str[len++] = '0';  // 使用t0控件显示坐标信息
	cmd_str[len++] = '.';
	cmd_str[len++] = 't';
	cmd_str[len++] = 'x';
	cmd_str[len++] = 't';
	cmd_str[len++] = '=';
	cmd_str[len++] = '"';

	// 显示Y坐标 (水平：右+左-)
	cmd_str[len++] = 'Y';
	cmd_str[len++] = ':';
	coord_int = (s16)Y;
	if(coord_int < 0) {
		cmd_str[len++] = '-';
		coord_int = -coord_int;
	}
	if(coord_int >= 100) cmd_str[len++] = '0' + (coord_int / 100);
	if(coord_int >= 10) cmd_str[len++] = '0' + ((coord_int % 100) / 10);
	cmd_str[len++] = '0' + (coord_int % 10);

	// 添加空格分隔符
	cmd_str[len++] = ' ';

	// 显示Z坐标 (垂直：上+下-)
	cmd_str[len++] = 'Z';
	cmd_str[len++] = ':';
	coord_int = (s16)Z;
	if(coord_int < 0) {
		cmd_str[len++] = '-';
		coord_int = -coord_int;
	}
	if(coord_int >= 100) cmd_str[len++] = '0' + (coord_int / 100);
	if(coord_int >= 10) cmd_str[len++] = '0' + ((coord_int % 100) / 10);
	cmd_str[len++] = '0' + (coord_int % 10);

	cmd_str[len++] = '"';
	cmd_str[len++] = 0xFF;
	cmd_str[len++] = 0xFF;
	cmd_str[len++] = 0xFF;

	USART2_SendArray(cmd_str, len);
	delay_ms(50);
	
	// 显示θ角度到文本框t1 (ID=5)
	len = 0;
	cmd_str[len++] = 't';
	cmd_str[len++] = '5';
	cmd_str[len++] = '.';
	cmd_str[len++] = 't';
	cmd_str[len++] = 'x';
	cmd_str[len++] = 't';
	cmd_str[len++] = '=';
	cmd_str[len++] = '"';
	cmd_str[len++] = 'T';
	cmd_str[len++] = ':';
	
	angle_int = (s16)theta_deg;
	if(angle_int < 0) {
		cmd_str[len++] = '-';
		angle_int = -angle_int;
	}
	if(angle_int >= 100) cmd_str[len++] = '0' + (angle_int / 100);
	if(angle_int >= 10) cmd_str[len++] = '0' + ((angle_int % 100) / 10);
	cmd_str[len++] = '0' + (angle_int % 10);
	cmd_str[len++] = 'd';
	
	cmd_str[len++] = '"';
	cmd_str[len++] = 0xFF;
	cmd_str[len++] = 0xFF;
	cmd_str[len++] = 0xFF;
	
	USART2_SendArray(cmd_str, len);
}

// ==================== 舵机角度反馈测试功能 ====================

// 全局变量用于存储接收到的舵机反馈数据
volatile u16 servo_feedback_position = 0;
volatile u8 servo_feedback_received = 0;

// 舵机位置读取函数
u16 Servo_ReadPosition(u8 servo_id)
{
	u8 cmd_packet[6];
	u16 timeout_count = 0;

	// 清除反馈标志
	servo_feedback_received = 0;
	servo_feedback_position = 0;

	// 构建读取位置命令包: 55 55 ID 03 1C checksum
	cmd_packet[0] = 0x55;								// 帧头1
	cmd_packet[1] = 0x55;								// 帧头2
	cmd_packet[2] = servo_id;							// 舵机ID
	cmd_packet[3] = 0x03;								// 数据长度
	cmd_packet[4] = 0x1C;								// 读取位置命令(SERVO_POS_READ = 28 = 0x1C)
	cmd_packet[5] = Servo_CalculateChecksum(&cmd_packet[2], 3);  // 校验和

	// 发送命令包
	USART_SendArray(cmd_packet, 6);

	// 等待反馈数据，最多等待100ms
	while(!servo_feedback_received && timeout_count < 1000) {
		delay_ms(1);
		timeout_count++;
	}

	if(servo_feedback_received) {
		return servo_feedback_position;
	} else {
		return 0xFFFF;  // 返回错误值表示读取失败
	}
}

// 舵机位置精度测试函数
void Servo_TestPositionAccuracy(u8 servo_id, u16 target_pos)
{
	u16 actual_pos;
	s16 error;

	// 发送位置命令
	Servo_MoveToPosition(servo_id, target_pos, 0);
	delay_ms(500);  // 等待舵机移动完成

	// 读取实际位置
	actual_pos = Servo_ReadPosition(servo_id);

	if(actual_pos != 0xFFFF) {
		error = (s16)actual_pos - (s16)target_pos;

		// 通过串口屏显示测试结果到t0文本框
		{
			u8 result_str[50];
			u8 len = 0;
			u16 temp_actual;  // C89兼容：变量声明必须在代码块开始

			result_str[len++] = 't'; result_str[len++] = '0'; result_str[len++] = '.';
			result_str[len++] = 't'; result_str[len++] = 'x'; result_str[len++] = 't';
			result_str[len++] = '='; result_str[len++] = '"';

			// 显示舵机ID
			result_str[len++] = 'S'; result_str[len++] = '0' + servo_id; result_str[len++] = ':';

			// 显示目标位置（简化显示）
			result_str[len++] = 'T'; result_str[len++] = '=';
			if(target_pos >= 1000) { result_str[len++] = '1'; target_pos -= 1000; }
			if(target_pos >= 100) { result_str[len++] = '0' + target_pos/100; target_pos %= 100; }
			result_str[len++] = '0' + target_pos/10; result_str[len++] = '0' + target_pos%10;

			result_str[len++] = ' ';

			// 显示实际位置
			result_str[len++] = 'A'; result_str[len++] = '=';
			temp_actual = actual_pos;
			if(temp_actual >= 1000) { result_str[len++] = '1'; temp_actual -= 1000; }
			if(temp_actual >= 100) { result_str[len++] = '0' + temp_actual/100; temp_actual %= 100; }
			result_str[len++] = '0' + temp_actual/10; result_str[len++] = '0' + temp_actual%10;

			result_str[len++] = ' ';

			// 显示误差
			result_str[len++] = 'E'; result_str[len++] = '=';
			if(error < 0) { result_str[len++] = '-'; error = -error; }
			else { result_str[len++] = '+'; }
			result_str[len++] = '0' + error/10; result_str[len++] = '0' + error%10;

			result_str[len++] = '"';
			result_str[len++] = 0xFF; result_str[len++] = 0xFF; result_str[len++] = 0xFF;
			USART2_SendArray(result_str, len);
		}
	} else {
		// 读取失败提示显示到t0文本框
		{
			u8 error_str[25];
			u8 len = 0;
			error_str[len++] = 't'; error_str[len++] = '0'; error_str[len++] = '.';
			error_str[len++] = 't'; error_str[len++] = 'x'; error_str[len++] = 't';
			error_str[len++] = '='; error_str[len++] = '"';
			error_str[len++] = 'R'; error_str[len++] = 'e'; error_str[len++] = 'a'; error_str[len++] = 'd';
			error_str[len++] = ' '; error_str[len++] = 'F'; error_str[len++] = 'a'; error_str[len++] = 'i'; error_str[len++] = 'l';
			error_str[len++] = '"';
			error_str[len++] = 0xFF; error_str[len++] = 0xFF; error_str[len++] = 0xFF;
			USART2_SendArray(error_str, len);
		}
	}
}

// 舵机角度反馈综合测试函数
void Servo_AngleFeedbackTest(void)
{
	// 测试多个位置点
	u16 test_positions[] = {200, 300, 500, 700, 800};  // 测试位置
	u8 test_count = sizeof(test_positions) / sizeof(test_positions[0]);
	u8 i;  // C89兼容：变量声明必须在代码块开始

	// 显示测试开始提示到t0文本框
	{
		u8 start_str[25];
		u8 len = 0;
		start_str[len++] = 't'; start_str[len++] = '0'; start_str[len++] = '.';
		start_str[len++] = 't'; start_str[len++] = 'x'; start_str[len++] = 't';
		start_str[len++] = '='; start_str[len++] = '"';
		start_str[len++] = 'A'; start_str[len++] = 'n'; start_str[len++] = 'g'; start_str[len++] = 'l'; start_str[len++] = 'e';
		start_str[len++] = ' '; start_str[len++] = 'T'; start_str[len++] = 'e'; start_str[len++] = 's'; start_str[len++] = 't';
		start_str[len++] = '"';
		start_str[len++] = 0xFF; start_str[len++] = 0xFF; start_str[len++] = 0xFF;
		USART2_SendArray(start_str, len);
	}
	delay_ms(1000);

	// 测试舵机1
	for(i = 0; i < test_count; i++) {
		Servo_TestPositionAccuracy(1, test_positions[i]);
		delay_ms(1500);  // 每次测试间隔
	}

	delay_ms(1000);

	// 测试舵机2
	for(i = 0; i < test_count; i++) {
		Servo_TestPositionAccuracy(2, test_positions[i]);
		delay_ms(1500);  // 每次测试间隔
	}

	// 显示测试完成提示到t0文本框
	{
		u8 end_str[25];
		u8 len = 0;
		end_str[len++] = 't'; end_str[len++] = '0'; end_str[len++] = '.';
		end_str[len++] = 't'; end_str[len++] = 'x'; end_str[len++] = 't';
		end_str[len++] = '='; end_str[len++] = '"';
		end_str[len++] = 'T'; end_str[len++] = 'e'; end_str[len++] = 's'; end_str[len++] = 't';
		end_str[len++] = ' '; end_str[len++] = 'D'; end_str[len++] = 'o'; end_str[len++] = 'n'; end_str[len++] = 'e';
		end_str[len++] = '"';
		end_str[len++] = 0xFF; end_str[len++] = 0xFF; end_str[len++] = 0xFF;
		USART2_SendArray(end_str, len);
	}
}
